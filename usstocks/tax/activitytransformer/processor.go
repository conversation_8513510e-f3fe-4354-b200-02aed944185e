//go:generate mockgen -source=processor.go -destination=./mocks/mock_processor.go -package=mocks

package activitytransformer

import (
	"context"
	"fmt"
	"sort"
	"time"

	"github.com/google/wire"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	datePb "google.golang.org/genproto/googleapis/type/date"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	ussTaxPb "github.com/epifi/gamma/api/usstocks/tax"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
)

var ActivitiesProcessorWireSet = wire.NewSet(
	NewProcessor, wire.Bind(new(ActivitiesProcessor), new(*ActivityTransformer)),
	NewCashProcessor, NewDividendProcessor, NewDividendAdjustmentProcessor, NewInterestProcessor, NewBuySellProcessor,
	NewStockSplitProcessor, NewStockSpinOffProcessor, NewStockNameChangeProcessor, NewAcquisitionProcessor, NewFeeProcessor)

type ActivityProcessor interface {
	// ProcessActivity processes the given Activity and returns a list of mutated tax Transactions
	// The input Transactions are expected to be sorted in ascending order of executed at time
	// The executed at of Activity to be processes should be either greater than or equal to last element of Transactions
	ProcessActivity(ctx context.Context, request *ProcessActivityRequest) (*ProcessActivityResponse, error)
	// ProcessSuspenseActivities processes a group of related non trade activities, evaluate and applies their impact on the existing transactions
	ProcessSuspenseActivities(ctx context.Context, req *ProcessSuspenseActivitiesRequest) (*ProcessSuspenseActivitiesResponse, error)
}

type UnHandledSuspenseActivities struct{}

func (u *UnHandledSuspenseActivities) ProcessSuspenseActivities(ctx context.Context, req *ProcessSuspenseActivitiesRequest) (*ProcessSuspenseActivitiesResponse, error) {
	return nil, fmt.Errorf("suspense activities are not handled")
}

type ActivitiesProcessor interface {
	// ProcessActivities processes a list of account Activities. Its expected that account Activities are exhaustive with no
	// missing activity for a date.
	ProcessActivities(ctx context.Context, activities []*vgStocksPb.AccountActivity) ([]*ussTaxPb.TransactionWrapper, error)
}

type ProcessActivityRequest struct {
	Activity     *vgStocksPb.AccountActivity
	Transactions []*ussTaxPb.TransactionWrapper
	// Ambiguous activities such as stock split, name change etc are grouped under suspense activities.
	// Suspense activities for a corporate event are evaluated as a unified group of activities and cannot be processed
	// independently
	// This is inferred  from the concept of suspense accounts where we catch all 'un-categorized' or 'ambiguous'
	// transactions and later correct them when more information is available
	// Eg A stock split is represented by a debit of all units of a stock and a credit qty remaining post split
	SuspenseActivities *SuspenseActivities
}

type ProcessActivityResponse struct {
	Transactions       []*ussTaxPb.TransactionWrapper
	SuspenseActivities *SuspenseActivities
}

type ProcessSuspenseActivitiesRequest struct {
	Transactions  []*ussTaxPb.TransactionWrapper
	ActivitiesMap map[string][]*vgStocksPb.NonTradeActivity
}

type ProcessSuspenseActivitiesResponse struct {
	Transactions []*ussTaxPb.TransactionWrapper
}

type SuspenseActivities struct {
	ExecutionTime time.Time
	// map of non trade activities belonging to a symbol
	NonTradeActivities map[vgStocksPb.NonTradeActivityType]map[string][]*vgStocksPb.NonTradeActivity
}

func NewSuspenseActivities(execTime time.Time) *SuspenseActivities {
	return &SuspenseActivities{
		ExecutionTime:      execTime,
		NonTradeActivities: make(map[vgStocksPb.NonTradeActivityType]map[string][]*vgStocksPb.NonTradeActivity),
	}
}

// AddActivity groups and adds a non trade activity to collection of suspense activities
func (s *SuspenseActivities) AddActivity(activity *vgStocksPb.AccountActivity) error {
	nta := activity.GetNonTradeActivity()
	if nta == nil {
		return fmt.Errorf("expected a non trade activity but got %v", activity.GetActivity())
	}

	if !activity.GetExecutedAt().Equal(s.ExecutionTime) {
		return fmt.Errorf("all suspense activities should belong to same time expected exec time %v but got %v", s.ExecutionTime, activity.GetExecutedAt())
	}

	if s.NonTradeActivities == nil {
		return fmt.Errorf("non trade activities map is not initialised")
	}

	ntaActivities, ok := s.NonTradeActivities[nta.GetType()]
	if !ok {
		ntaActivities = map[string][]*vgStocksPb.NonTradeActivity{}
		s.NonTradeActivities[nta.GetType()] = ntaActivities
	}

	ntaActivities[nta.GetSymbol()] = append(ntaActivities[nta.GetSymbol()], nta)

	return nil
}

type ActivityTransformer struct {
	nonTradeActivityProcessor map[vgStocksPb.NonTradeActivityType]ActivityProcessor
	tradeActivityProcessor    map[vgStocksPb.Side]ActivityProcessor
}

func NewProcessor(buySellProcessor *BuySellProcessor,
	dividendProcessor *DividendProcessor,
	dividendAdjustmentProcessor *DividendAdjustmentProcessor,
	interestProcessor *InterestProcessor,
	cashProcessor *CashProcessor,
	feeProcessor *FeeProcessor,
	stockSplitProcessor *StockSplitProcessor,
	stockNameChangeProcessor *StockNameChangeProcessor,
	stockSpinOffProcessor *StockSpinOffProcessor,
	acquisitionProcessor *AcquisitionProcessor) *ActivityTransformer {
	return &ActivityTransformer{
		nonTradeActivityProcessor: map[vgStocksPb.NonTradeActivityType]ActivityProcessor{
			vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_DIVIDEND:            dividendProcessor,
			vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_DIVIDEND_ADJUSTMENT: dividendAdjustmentProcessor,
			vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_INTEREST:            interestProcessor,
			vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_TRANSFER_CASH:       cashProcessor,
			vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_ACAT_CASH:           cashProcessor,
			vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_FEE:                 feeProcessor,
			vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT:         stockSplitProcessor,
			vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_NAME_CHANGE:   stockNameChangeProcessor,
			vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPIN_OFF:      stockSpinOffProcessor,
			vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_ACQUISITION:         acquisitionProcessor,
		},
		tradeActivityProcessor: map[vgStocksPb.Side]ActivityProcessor{
			vgStocksPb.Side_SIDE_SELL: buySellProcessor,
			vgStocksPb.Side_SIDE_BUY:  buySellProcessor,
		},
	}
}

func (b *ActivityTransformer) ProcessActivities(ctx context.Context, activities []*vgStocksPb.AccountActivity) ([]*ussTaxPb.TransactionWrapper, error) {
	sort.Slice(activities, func(i, j int) bool {
		return activities[i].GetExecutedAt().Before(activities[j].GetExecutedAt())
	})
	// TODO: remove this log post testing of failure scenarios
	logger.Info(ctx, "number of activities to process", zap.Any(logger.COUNT, len(activities)))
	res := &ProcessActivityResponse{}
	for _, activity := range activities {
		var err error
		res, err = b.ProcessActivity(ctx, &ProcessActivityRequest{Activity: activity, Transactions: res.Transactions, SuspenseActivities: res.SuspenseActivities})
		if err != nil {
			return nil, fmt.Errorf("failed to process activity: %w", err)
		}
	}
	if res.SuspenseActivities != nil {
		var err error
		// process all pending suspense activities before moving forward
		res, err = b.processSuspenseActivities(ctx, &ProcessActivityRequest{Transactions: res.Transactions, SuspenseActivities: res.SuspenseActivities})
		if err != nil {
			return nil, fmt.Errorf("failed to process suspense activities: %w", err)
		}
	}
	return res.Transactions, nil
}

func (b *ActivityTransformer) ProcessActivity(ctx context.Context, req *ProcessActivityRequest) (*ProcessActivityResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("process Activity request cannot be nil")
	}
	// TODO: remove post testing
	logger.Info(ctx, "processing tax activity", zap.String(logger.ACTIVITY_ID, req.Activity.GetActivityId()), zap.Any(logger.REQUEST_TIME, req.Activity.GetExecutedAt()))
	if req.SuspenseActivities != nil && req.SuspenseActivities.ExecutionTime.Before(req.Activity.GetExecutedAt()) {
		// process all suspense activities before moving forward
		res, err := b.processSuspenseActivities(ctx, req)
		if err != nil {
			return nil, fmt.Errorf("failed to process suspense activities: %w", err)
		}
		req = &ProcessActivityRequest{Transactions: res.Transactions, SuspenseActivities: res.SuspenseActivities, Activity: req.Activity}
	}
	switch req.Activity.GetActivity().(type) {
	case *vgStocksPb.AccountActivity_TradeActivity:
		return b.processTradeActivity(ctx, req)
	case *vgStocksPb.AccountActivity_NonTradeActivity:
		return b.processNonTradeActivity(ctx, req)
	default:
		return nil, fmt.Errorf("unhandled Activity type %T", req.Activity.GetActivity())
	}
}

func (b *ActivityTransformer) processTradeActivity(ctx context.Context, req *ProcessActivityRequest) (*ProcessActivityResponse, error) {
	tradeActivity := req.Activity.GetTradeActivity()
	processor, ok := b.tradeActivityProcessor[tradeActivity.GetSide()]
	if !ok {
		return nil, fmt.Errorf("unhandled trade Activity side %v for Activity id %s", tradeActivity.GetSide(), tradeActivity.GetId())
	}

	res, err := processor.ProcessActivity(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("error while processing Activity id %s: %w", tradeActivity.GetId(), err)
	}
	return res, nil
}

func (b *ActivityTransformer) processNonTradeActivity(ctx context.Context, req *ProcessActivityRequest) (*ProcessActivityResponse, error) {
	nonTradeActivity := req.Activity.GetNonTradeActivity()
	processor, ok := b.nonTradeActivityProcessor[nonTradeActivity.GetType()]
	if !ok {
		return nil, fmt.Errorf("unhandled non trade activity type %v for activity id %s", nonTradeActivity.GetType(), nonTradeActivity.GetId())
	}

	res, err := processor.ProcessActivity(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("error while processing activity id %s: %w", nonTradeActivity.GetId(), err)
	}
	return res, nil
}

func (b *ActivityTransformer) processSuspenseActivities(ctx context.Context, req *ProcessActivityRequest) (*ProcessActivityResponse, error) {
	if req.SuspenseActivities == nil {
		return nil, fmt.Errorf("suspense activities cannot be nil")
	}
	res := &ProcessSuspenseActivitiesResponse{Transactions: req.Transactions}
	for activityType, activities := range req.SuspenseActivities.NonTradeActivities {
		processor, ok := b.nonTradeActivityProcessor[activityType]
		if !ok {
			return nil, fmt.Errorf("unhandled non trade activity type %v for suspense activities", activityType)
		}
		var err error
		res, err = processor.ProcessSuspenseActivities(ctx, &ProcessSuspenseActivitiesRequest{Transactions: res.Transactions, ActivitiesMap: activities})
		if err != nil {
			return nil, fmt.Errorf("failed to process suspense activities of type %v for date %v: %w", activityType, req.SuspenseActivities.ExecutionTime, err)
		}
	}
	return &ProcessActivityResponse{
		Transactions: res.Transactions,
	}, nil
}

func createNonTradeTransaction(activity *vgStocksPb.AccountActivity, transactionType ussTaxPb.TransactionType) *ussTaxPb.TransactionWrapper {
	nonTradeActivity := activity.GetNonTradeActivity()
	qty := decimal.NewFromFloat(nonTradeActivity.GetDividendMetaInfo().GetQty())
	return &ussTaxPb.TransactionWrapper{
		Transaction: &ussTaxPb.Transaction{
			Symbol:           nonTradeActivity.GetSymbol(),
			Amount:           nonTradeActivity.GetNetAmount(),
			ExecutedAt:       dateToEstTimestamp(nonTradeActivity.GetExecutedDate()),
			TransactionType:  transactionType,
			ParentActivities: []*vgStocksPb.AccountActivity{activity},
		},
		Quantity: qty,
	}
}

func dateToEstTimestamp(date *datePb.Date) *timestampPb.Timestamp {
	return timestampPb.New(time.Date(int(date.GetYear()), time.Month(date.GetMonth()), int(date.GetDay()), 0, 0, 0, 0, datetime.EST5EDT))
}

func processQtySuspenseActivity(req *ProcessActivityRequest, activityType vgStocksPb.NonTradeActivityType) (*ProcessActivityResponse, error) {
	activity := req.Activity
	nonTradeActivity := activity.GetNonTradeActivity()
	if nonTradeActivity.GetType() != activityType {
		return nil, fmt.Errorf("unhandled %T Activity of type %v", activity.GetActivity(), nonTradeActivity.GetType())
	}

	if nonTradeActivity.GetStatus() == vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_CANCELED {
		return NewProcessActivityResponse(req.Transactions, req.SuspenseActivities).appendTxn(createNonTradeTransaction(activity, ussTaxPb.TransactionType_TRANSACTION_TYPE_NO_OP)), nil
	}

	if nonTradeActivity.GetStatus() != vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_EXECUTED {
		return nil, fmt.Errorf("expected %v status for qty activity", vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_EXECUTED)
	}

	if !money.IsZero(nonTradeActivity.GetNetAmount()) {
		return nil, fmt.Errorf("expected zero amount in qty activity")
	}

	suspenseActivities := req.SuspenseActivities
	if suspenseActivities == nil {
		suspenseActivities = NewSuspenseActivities(activity.GetExecutedAt())
	}
	err := suspenseActivities.AddActivity(activity)
	if err != nil {
		return nil, fmt.Errorf("failed to add suspense activity: %w", err)
	}
	return NewProcessActivityResponse(req.Transactions, suspenseActivities), nil
}

func NewProcessActivityResponse(txns []*ussTaxPb.TransactionWrapper, susActivities *SuspenseActivities) *ProcessActivityResponse {
	return &ProcessActivityResponse{
		Transactions:       txns,
		SuspenseActivities: susActivities,
	}
}

func (p *ProcessActivityResponse) appendTxn(txn *ussTaxPb.TransactionWrapper) *ProcessActivityResponse {
	if p == nil {
		return &ProcessActivityResponse{
			Transactions: []*ussTaxPb.TransactionWrapper{
				txn,
			},
		}
	}
	p.Transactions = append(p.Transactions, txn)
	return p
}
