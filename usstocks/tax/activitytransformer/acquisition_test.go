package activitytransformer

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"testing"
	"time"

	"github.com/go-test/deep"
	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/shopspring/decimal"
	datePb "google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	ussTaxPb "github.com/epifi/gamma/api/usstocks/tax"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"
)

func TestAcquisitionProcessor_ProcessActivity(t *testing.T) {
	tests := []struct {
		name    string
		req     *ProcessActivityRequest
		want    func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse
		wantErr bool
	}{
		{
			name: "Successfully process acquisition activity",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_NonTradeActivity{
						NonTradeActivity: &vgStocksPb.NonTradeActivity{
							Id:               "id1",
							AccountId:        "acctId",
							Type:             vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_ACQUISITION,
							ExecutedDate:     &datePb.Date{Year: 2024, Month: 11, Day: 12},
							Description:      "acquisition",
							Symbol:           "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{Qty: -10},
							Status:           vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				return &ProcessActivityResponse{
					SuspenseActivities: &SuspenseActivities{
						ExecutionTime: activity.GetExecutedAt(),
						NonTradeActivities: map[vgStocksPb.NonTradeActivityType]map[string][]*vgStocksPb.NonTradeActivity{
							vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_ACQUISITION: {
								"APPL": []*vgStocksPb.NonTradeActivity{
									activity.GetNonTradeActivity(),
								},
							},
						},
					},
				}
			},
		},
		{
			name: "Cancelled status should lead to no op txn",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_NonTradeActivity{
						NonTradeActivity: &vgStocksPb.NonTradeActivity{
							Id:               "id1",
							AccountId:        "acctId",
							Type:             vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_ACQUISITION,
							ExecutedDate:     &datePb.Date{Year: 2024, Month: 11, Day: 12},
							Description:      "acquisition",
							Symbol:           "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{Qty: -10},
							Status:           vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_CANCELED,
						},
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				return &ProcessActivityResponse{
					Transactions: []*ussTaxPb.TransactionWrapper{createNonTradeTransaction(activity, ussTaxPb.TransactionType_TRANSACTION_TYPE_NO_OP)},
				}
			},
		},
		{
			name: "Executed status should lead to error",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_NonTradeActivity{
						NonTradeActivity: &vgStocksPb.NonTradeActivity{
							Id:               "id1",
							AccountId:        "acctId",
							Type:             vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_ACQUISITION,
							ExecutedDate:     &datePb.Date{Year: 2024, Month: 11, Day: 12},
							Description:      "acquisition",
							Symbol:           "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{Qty: -10},
							Status:           vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_EXECUTED,
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "Negative net amt should lead to error",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_NonTradeActivity{
						NonTradeActivity: &vgStocksPb.NonTradeActivity{
							Id:           "id1",
							AccountId:    "acctId",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_ACQUISITION,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 11, Day: 12},
							NetAmount:    money.AmountUSD(-10),
							Description:  "acquisition",
							Symbol:       "APPL",
							Status:       vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			wantErr: true,
		},

		{
			name: "Incorrect activity type should lead to error",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_NonTradeActivity{
						NonTradeActivity: &vgStocksPb.NonTradeActivity{
							Id:           "id1",
							AccountId:    "acctId",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPIN_OFF,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 11, Day: 12},
							Description:  "acquisition",
							Symbol:       "APPL",
							Status:       vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			b := NewAcquisitionProcessor(nil)
			got, err := b.ProcessActivity(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessActivity() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr {
				return
			}
			if diff := cmp.Diff(got, tt.want(tt.req.Activity), protocmp.Transform()); diff != "" {
				t.Errorf("ProcessActivity() diff=%v", diff)
			}
		})
	}
}

func TestAcquisitionProcessor_ProcessSuspenseActivities(t *testing.T) {
	cashCorporateAction := &vgStocksPb.CorporateActions{
		CashMergers: []*vgStocksPb.CashMerger{
			{
				AcquireeSymbol: "APPL",
				EffectiveDate:  &datePb.Date{Year: 2024, Month: 2, Day: 5},
				PayableDate:    &datePb.Date{Year: 2024, Month: 2, Day: 5},
				ProcessDate:    &datePb.Date{Year: 2024, Month: 2, Day: 5},
				Rate:           2.4,
				AcquirerSymbol: "APL",
			},
		},
	}
	tests := []struct {
		name    string
		req     *ProcessSuspenseActivitiesRequest
		before  func(m *mockFields)
		want    func(activitiesMap map[string][]*vgStocksPb.NonTradeActivity) *ProcessSuspenseActivitiesResponse
		wantErr bool
	}{
		{
			name: "Successfully process cash acquisition activities",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol: "APPL",
						},
					},
				},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{
							Id:               "id1",
							AccountId:        "acctId",
							Type:             vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_ACQUISITION,
							ExecutedDate:     &datePb.Date{Year: 2024, Month: 2, Day: 5},
							NetAmount:        nil,
							Description:      "acquisition",
							Symbol:           "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{Qty: -10},
							Status:           vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
						{
							Id:           "id2",
							AccountId:    "acctId",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_ACQUISITION,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 2, Day: 5},
							NetAmount:    money.AmountUSD(24),
							Description:  "acquisition",
							Symbol:       "APPL",
							Status:       vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockVgStocksClient.EXPECT().GetCorporateActions(gomock.Any(), &vgStocksPb.GetCorporateActionsRequest{
					Header:  &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ALPACA},
					Symbols: []string{"APPL"},
					Types: []vgStocksPb.CorporateActionType{vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_CASH_MERGER,
						vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_STOCK_MERGER, vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_STOCK_AND_CASH_MERGER},
					Start: &datePb.Date{Year: 2024, Month: 2, Day: 5},
					Limit: 10,
				}).Return(&vgStocksPb.GetCorporateActionsResponse{
					Status:           rpcPb.StatusOk(),
					CorporateActions: cashCorporateAction,
				}, nil)
			},
			want: func(activitiesMap map[string][]*vgStocksPb.NonTradeActivity) *ProcessSuspenseActivitiesResponse {
				activities := nonTradeActivityAsAccountActivity(activitiesMap["APPL"])
				return &ProcessSuspenseActivitiesResponse{
					Transactions: []*ussTaxPb.TransactionWrapper{
						{
							Transaction: &ussTaxPb.Transaction{Symbol: "APPL"},
						},
						{
							Transaction: &ussTaxPb.Transaction{
								Symbol:           "APPL",
								Amount:           money.AmountUSD(24),
								ExecutedAt:       timestampPb.New(time.Date(2024, 02, 05, 0, 0, 0, 0, datetime.EST5EDT)),
								TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_SELL,
								Expense:          money.ZeroUSD(),
								ParentActivities: activities,
							},
							Quantity: decimal.NewFromFloat(-10),
						},
					},
				}
			},
		},
		{
			name: "Missing qty debit txn while processing cash acquisition activities should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol: "APPL",
						},
					},
				},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{
							Id:           "id2",
							AccountId:    "acctId",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_ACQUISITION,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 2, Day: 5},
							NetAmount:    money.AmountUSD(24),
							Description:  "acquisition",
							Symbol:       "APPL",
							Status:       vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
						{Id: "id1"},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockVgStocksClient.EXPECT().GetCorporateActions(gomock.Any(), gomock.Any()).Return(&vgStocksPb.GetCorporateActionsResponse{
					Status:           rpcPb.StatusOk(),
					CorporateActions: cashCorporateAction,
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Missing amount credit while processing cash acquisition activities should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol: "APPL",
						},
					},
				},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{
							Id:               "id1",
							AccountId:        "acctId",
							Type:             vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_ACQUISITION,
							ExecutedDate:     &datePb.Date{Year: 2024, Month: 2, Day: 5},
							NetAmount:        nil,
							Description:      "acquisition",
							Symbol:           "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{Qty: -10},
							Status:           vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
						{Id: "id2"},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockVgStocksClient.EXPECT().GetCorporateActions(gomock.Any(), gomock.Any()).Return(&vgStocksPb.GetCorporateActionsResponse{
					Status:           rpcPb.StatusOk(),
					CorporateActions: cashCorporateAction,
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Failure to get corporate actions should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol: "APPL",
						},
					},
				},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{Id: "id2", ExecutedDate: &datePb.Date{Year: 2024, Month: 2, Day: 5}},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockVgStocksClient.EXPECT().GetCorporateActions(gomock.Any(), gomock.Any()).Return(&vgStocksPb.GetCorporateActionsResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "No corporate actions found should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol: "APPL",
						},
					},
				},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{Id: "id2"},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockVgStocksClient.EXPECT().GetCorporateActions(gomock.Any(), gomock.Any()).Return(&vgStocksPb.GetCorporateActionsResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "More than one cash merger corporate actions found should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol: "APPL",
						},
					},
				},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{Id: "id2"},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockVgStocksClient.EXPECT().GetCorporateActions(gomock.Any(), gomock.Any()).Return(&vgStocksPb.GetCorporateActionsResponse{
					Status: rpcPb.StatusOk(),
					CorporateActions: &vgStocksPb.CorporateActions{
						CashMergers: []*vgStocksPb.CashMerger{{}, {}},
					},
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Stock merger corporate actions found should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{Id: "id2"},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockVgStocksClient.EXPECT().GetCorporateActions(gomock.Any(), gomock.Any()).Return(&vgStocksPb.GetCorporateActionsResponse{
					Status: rpcPb.StatusOk(),
					CorporateActions: &vgStocksPb.CorporateActions{
						StockMergers: []*vgStocksPb.StockMerger{
							{
								AcquireeSymbol: "APPL",
								AcquirerSymbol: "APL",
							},
						},
					},
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "More than one stock merger corporate actions found should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{Id: "id2"},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockVgStocksClient.EXPECT().GetCorporateActions(gomock.Any(), gomock.Any()).Return(&vgStocksPb.GetCorporateActionsResponse{
					Status: rpcPb.StatusOk(),
					CorporateActions: &vgStocksPb.CorporateActions{
						StockMergers: []*vgStocksPb.StockMerger{{}, {}},
					},
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Stock and cash merger corporate actions found should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{Id: "id2"},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockVgStocksClient.EXPECT().GetCorporateActions(gomock.Any(), gomock.Any()).Return(&vgStocksPb.GetCorporateActionsResponse{
					Status: rpcPb.StatusOk(),
					CorporateActions: &vgStocksPb.CorporateActions{
						StockAndCashMergers: []*vgStocksPb.StockAndCashMerger{
							{
								AcquireeSymbol: "APPL",
								AcquirerSymbol: "APL",
							},
						},
					},
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "More than one stock and cash merger corporate actions found should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{Id: "id2"},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockVgStocksClient.EXPECT().GetCorporateActions(gomock.Any(), gomock.Any()).Return(&vgStocksPb.GetCorporateActionsResponse{
					Status: rpcPb.StatusOk(),
					CorporateActions: &vgStocksPb.CorporateActions{
						StockAndCashMergers: []*vgStocksPb.StockAndCashMerger{{}, {}},
					},
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Both stock and cash merger corporate actions found should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{Id: "id2"},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockVgStocksClient.EXPECT().GetCorporateActions(gomock.Any(), gomock.Any()).Return(&vgStocksPb.GetCorporateActionsResponse{
					Status: rpcPb.StatusOk(),
					CorporateActions: &vgStocksPb.CorporateActions{
						StockMergers: []*vgStocksPb.StockMerger{
							{
								AcquireeSymbol: "APPL",
								AcquirerSymbol: "APL",
							},
						},
						CashMergers: []*vgStocksPb.CashMerger{
							{
								AcquireeSymbol: "APPL",
								AcquirerSymbol: "APL",
							},
						},
					},
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Both stock and cash merger corporate actions found should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{Id: "id2"},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockVgStocksClient.EXPECT().GetCorporateActions(gomock.Any(), gomock.Any()).Return(&vgStocksPb.GetCorporateActionsResponse{
					Status: rpcPb.StatusOk(),
					CorporateActions: &vgStocksPb.CorporateActions{
						StockMergers: []*vgStocksPb.StockMerger{
							{
								AcquireeSymbol: "APPL",
								AcquirerSymbol: "APL",
							},
						},
						CashMergers: []*vgStocksPb.CashMerger{
							{
								AcquireeSymbol: "APPL",
								AcquirerSymbol: "APL",
							},
						},
					},
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Both stock-cash and cash merger corporate actions found should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{Id: "id2"},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockVgStocksClient.EXPECT().GetCorporateActions(gomock.Any(), gomock.Any()).Return(&vgStocksPb.GetCorporateActionsResponse{
					Status: rpcPb.StatusOk(),
					CorporateActions: &vgStocksPb.CorporateActions{
						StockAndCashMergers: []*vgStocksPb.StockAndCashMerger{
							{
								AcquireeSymbol: "APPL",
								AcquirerSymbol: "APL",
							},
						},
						CashMergers: []*vgStocksPb.CashMerger{
							{
								AcquireeSymbol: "APPL",
								AcquirerSymbol: "APL",
							},
						},
					},
				}, nil)
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctl := gomock.NewController(t)
			defer ctl.Finish()
			m := initMocks(ctl)
			tt.before(m)
			s := NewAcquisitionProcessor(m.mockVgStocksClient)
			got, err := s.ProcessSuspenseActivities(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessSuspenseActivities() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr {
				return
			}
			if diff := deep.Equal(got, tt.want(tt.req.ActivitiesMap)); len(diff) != 0 {
				t.Errorf("ProcessSuspenseActivities() got = %v,\n want %v,\n diff %v", got, tt.want(tt.req.ActivitiesMap), diff)
			}
		})
	}
}
