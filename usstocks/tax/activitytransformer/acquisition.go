package activitytransformer

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"github.com/davecgh/go-spew/spew"
	"github.com/shopspring/decimal"
	datePb "google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/money"

	ussTaxPb "github.com/epifi/gamma/api/usstocks/tax"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
)

type AcquisitionProcessor struct {
	vgStocksClient vgStocksPb.StocksClient
}

func NewAcquisitionProcessor(vgStocksClient vgStocksPb.StocksClient) *AcquisitionProcessor {
	return &AcquisitionProcessor{
		vgStocksClient: vgStocksClient,
	}
}

func (a *AcquisitionProcessor) ProcessActivity(ctx context.Context, req *ProcessActivityRequest) (*ProcessActivityResponse, error) {
	activity := req.Activity
	nonTradeActivity := activity.GetNonTradeActivity()
	if nonTradeActivity.GetType() != vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_ACQUISITION {
		return nil, fmt.Errorf("unhandled %T activity of type %v", activity.GetActivity(), nonTradeActivity.GetType())
	}

	if nonTradeActivity.GetStatus() == vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_CANCELED {
		return NewProcessActivityResponse(req.Transactions, req.SuspenseActivities).appendTxn(createNonTradeTransaction(activity, ussTaxPb.TransactionType_TRANSACTION_TYPE_NO_OP)), nil
	}

	if nonTradeActivity.GetStatus() != vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_EXECUTED {
		return nil, fmt.Errorf("expected %v status for acquisition activity", vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_EXECUTED)
	}

	if money.IsNegative(nonTradeActivity.GetNetAmount()) {
		return nil, fmt.Errorf("unexpected negative amount in acquisition activity")
	}

	suspenseActivities := req.SuspenseActivities
	if suspenseActivities == nil {
		suspenseActivities = NewSuspenseActivities(activity.GetExecutedAt())
	}
	err := suspenseActivities.AddActivity(activity)
	if err != nil {
		return nil, fmt.Errorf("failed to add suspense activity: %w", err)
	}
	return NewProcessActivityResponse(req.Transactions, suspenseActivities), nil
}

func (a *AcquisitionProcessor) ProcessSuspenseActivities(ctx context.Context, req *ProcessSuspenseActivitiesRequest) (*ProcessSuspenseActivitiesResponse, error) {
	txn := req.Transactions
	for symbol := range req.ActivitiesMap {
		var err error
		txn, err = a.processAcquisitionActivities(ctx, symbol, txn, req.ActivitiesMap)
		if err != nil {
			return nil, fmt.Errorf("failed to process acquisition activities for %s: %w", symbol, err)
		}
	}
	return &ProcessSuspenseActivitiesResponse{Transactions: txn}, nil
}

func (a *AcquisitionProcessor) processAcquisitionActivities(ctx context.Context, symbol string, txns []*ussTaxPb.TransactionWrapper, activitiesMap map[string][]*vgStocksPb.NonTradeActivity) ([]*ussTaxPb.TransactionWrapper, error) {
	activities, ok := activitiesMap[symbol]
	if !ok {
		return nil, fmt.Errorf("no activities found for symbol %s", symbol)
	}
	if len(activities) == 0 {
		return nil, fmt.Errorf("expected atlead one acquisition activity for symbol %v", symbol)
	}
	acquisitionDetail, err := a.getAcquisitionDetails(ctx, symbol, activities[0].GetExecutedDate())
	if err != nil {
		return nil, fmt.Errorf("failed to get acquisition details for symbol %s on %v : %w", symbol, activities[0].GetExecutedDate(), err)
	}

	switch {
	case len(acquisitionDetail.AcquisitionTypes) == 1 && acquisitionDetail.ContainsAcquisitionType(vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_CASH_MERGER):
		txns, err = a.processCashOnlyMerger(symbol, txns, activities)
		if err != nil {
			return nil, fmt.Errorf("failed to process cash-only merger for symbole %v: %w", symbol, err)
		}
	case len(acquisitionDetail.AcquisitionTypes) == 1 && acquisitionDetail.ContainsAcquisitionType(vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_STOCK_MERGER):
		// stock only merger needs to evaluated as a sell txn on acquired symbol and buy txn on acquirer symbol
		// the amount of sell txn will be the end of day value of acquirer stock credited on the merger day
		// the amount of buy txn will be the end of day value of acquirer stock credited on the merger day
		// ToDo: Process this scenario one sufficient sample of account activities are available
		return nil, fmt.Errorf("unhandled stock merger scenario for symbol %v", symbol)
	case len(acquisitionDetail.AcquisitionTypes) == 1 && acquisitionDetail.ContainsAcquisitionType(vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_STOCK_AND_CASH_MERGER):
		// stock and cash merger needs to evaluated as a sell txn on acquired symbol and buy txn (of partial value) on acquirer symbol
		// the amount of sell txn will be the (end of day value of acquirer stock credited on the merger day ) + (cash credited for acquired symbol)
		// the amount of buy txn will be the end of day value of acquirer stock credited on the merger day
		// ToDo: Process this scenario one sufficient sample of account activities are available
		return nil, fmt.Errorf("unhandled stock and cash merger scenario for symbol %v", symbol)
	case len(acquisitionDetail.AcquisitionTypes) == 2 &&
		acquisitionDetail.ContainsAcquisitionType(vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_STOCK_MERGER) &&
		acquisitionDetail.ContainsAcquisitionType(vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_CASH_MERGER):
		// stock and/or cash merger is a scenario where acquirer company offered shareholders choice to take either cash, stock or both in some ratio
		// Eg Vmware(VMW) acquisition by Broadcom(AVGO)
		// It will be handled similar to stock and cash merger however the expected sequence of account activities is unknown
		// ToDo: Process this scenario one sufficient sample of account activities are available
		return nil, fmt.Errorf("unhandled stock and/or cash merger scenario for symbol %v", symbol)
	default:
		return nil, fmt.Errorf("unhandled acquisition scenario %v", spew.Sprint(acquisitionDetail))
	}

	return txns, nil
}

// processCashOnlyMerger evaluate a cash only merger activities
// cash merger is evaluated as a sell txn on acquired symbol. the amount of txn will be the amount received in lieu of acquisition
// only 2 activities one for qty debit and one for amount credit are expected. It'll be evaluated to a sell txn from tax perspective
func (a *AcquisitionProcessor) processCashOnlyMerger(symbol string, txns []*ussTaxPb.TransactionWrapper, nonTradeActivities []*vgStocksPb.NonTradeActivity) ([]*ussTaxPb.TransactionWrapper, error) {
	if len(nonTradeActivities) != 2 {
		return nil, fmt.Errorf("expected 2 nonTradeActivities for symbol %v but got %d", symbol, len(nonTradeActivities))
	}
	var qtyDebit float64
	var amtCredit *moneyPb.Money
	for _, activity := range nonTradeActivities {
		if a.isQtyDebitActivity(activity) {
			qtyDebit = activity.GetDividendMetaInfo().GetQty()
		}
		if a.isAmountCreditActivity(activity) {
			amtCredit = activity.GetNetAmount()
		}
	}

	if !money.IsPositive(amtCredit) {
		return nil, fmt.Errorf("expected amount credit to be positive")
	}
	if qtyDebit >= 0 {
		return nil, fmt.Errorf("expected qty debit to be negative")
	}

	activities := nonTradeActivityAsAccountActivity(nonTradeActivities)
	return append(txns, &ussTaxPb.TransactionWrapper{
		Transaction: &ussTaxPb.Transaction{
			Symbol:           symbol,
			Amount:           amtCredit,
			ExecutedAt:       timestampPb.New(activities[0].GetExecutedAt()),
			TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_SELL,
			Expense:          money.ZeroUSD(),
			ParentActivities: activities,
		},
		Quantity: decimal.NewFromFloat(qtyDebit),
	}), nil
}

func (a *AcquisitionProcessor) isQtyDebitActivity(activity *vgStocksPb.NonTradeActivity) bool {
	return activity.GetDividendMetaInfo().GetQty() < 0
}

func (a *AcquisitionProcessor) isAmountCreditActivity(activity *vgStocksPb.NonTradeActivity) bool {
	return money.IsPositive(activity.GetNetAmount())
}

type AcquisitionDetails struct {
	AcquireSymbol, AcquirerSymbol string
	AcquisitionTypes              map[vgStocksPb.CorporateActionType]any
}

func NewAcquisitionDetails(acquireStock, acquiredStock string, acquisitionType vgStocksPb.CorporateActionType) *AcquisitionDetails {
	return &AcquisitionDetails{
		AcquireSymbol:    acquireStock,
		AcquirerSymbol:   acquiredStock,
		AcquisitionTypes: map[vgStocksPb.CorporateActionType]any{acquisitionType: nil},
	}
}

func (a *AcquisitionDetails) ContainsAcquisitionType(acquisitionType vgStocksPb.CorporateActionType) bool {
	if a.AcquisitionTypes == nil {
		return false
	}
	_, ok := a.AcquisitionTypes[acquisitionType]
	return ok
}

func (a *AcquisitionProcessor) getAcquisitionDetails(ctx context.Context, symbol string, date *datePb.Date) (*AcquisitionDetails, error) {
	res, err := a.vgStocksClient.GetCorporateActions(ctx, &vgStocksPb.GetCorporateActionsRequest{
		Header:  &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ALPACA},
		Symbols: []string{symbol},
		Types: []vgStocksPb.CorporateActionType{vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_CASH_MERGER,
			vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_STOCK_MERGER, vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_STOCK_AND_CASH_MERGER},
		Start: date,
		Limit: 10,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return nil, fmt.Errorf("failed to get acquisition symbols: %w", rpcErr)
	}

	var acquisitionDetails *AcquisitionDetails

	if stockMergers := res.GetCorporateActions().GetStockMergers(); len(stockMergers) > 0 {
		if l := len(stockMergers); l > 1 {
			return nil, fmt.Errorf("expected 1 corporate action for stock merger but got %d", l)
		}
		acquisitionDetails = NewAcquisitionDetails(stockMergers[0].GetAcquireeSymbol(), stockMergers[0].GetAcquirerSymbol(),
			vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_STOCK_MERGER)
	}

	if stockAndCashMergers := res.GetCorporateActions().GetStockAndCashMergers(); len(stockAndCashMergers) > 0 {
		if l := len(stockAndCashMergers); l > 1 {
			return nil, fmt.Errorf("expected 1 corporate action for stock and cash merger but got %d", l)
		}
		if acquisitionDetails == nil {
			acquisitionDetails = NewAcquisitionDetails(stockAndCashMergers[0].GetAcquireeSymbol(), stockAndCashMergers[0].GetAcquirerSymbol(),
				vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_STOCK_AND_CASH_MERGER)
		} else {
			acquisitionDetails.AcquisitionTypes[vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_STOCK_AND_CASH_MERGER] = nil
		}
	}

	if cashMergers := res.GetCorporateActions().GetCashMergers(); len(cashMergers) > 0 {
		if l := len(cashMergers); l > 1 {
			return nil, fmt.Errorf("expected 1 corporate action for cash merger but got %d", l)
		}
		if acquisitionDetails == nil {
			acquisitionDetails = NewAcquisitionDetails(cashMergers[0].GetAcquireeSymbol(), cashMergers[0].GetAcquirerSymbol(),
				vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_CASH_MERGER)
		} else {
			acquisitionDetails.AcquisitionTypes[vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_CASH_MERGER] = nil
		}
	}

	if acquisitionDetails == nil {
		return nil, epifierrors.ErrRecordNotFound
	}

	return acquisitionDetails, nil
}
