package activitytransformer

import (
	"context"
	"testing"
	"time"

	"github.com/go-test/deep"
	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	datePb "google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"

	ussTaxPb "github.com/epifi/gamma/api/usstocks/tax"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
)

func TestStockSplitProcessor_ProcessActivity(t *testing.T) {
	tests := []struct {
		name    string
		req     *ProcessActivityRequest
		want    func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse
		wantErr bool
	}{
		{
			name: "Successfully process stock split activity",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_NonTradeActivity{
						NonTradeActivity: &vgStocksPb.NonTradeActivity{
							Id:           "id1",
							AccountId:    "acctId",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 02, Day: 05},
							Description:  "stock split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: 10,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_EXECUTED,
						},
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				return &ProcessActivityResponse{
					SuspenseActivities: &SuspenseActivities{
						ExecutionTime: activity.GetExecutedAt(),
						NonTradeActivities: map[vgStocksPb.NonTradeActivityType]map[string][]*vgStocksPb.NonTradeActivity{
							vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT: {
								"APPL": []*vgStocksPb.NonTradeActivity{
									activity.GetNonTradeActivity(),
								},
							},
						},
					},
				}
			},
		},
		{
			name: "Canceled activity should lead to no-op transaction",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_NonTradeActivity{
						NonTradeActivity: &vgStocksPb.NonTradeActivity{
							Id:           "id1",
							AccountId:    "acctId",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 02, Day: 05},
							Description:  "stock split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: 10,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_CANCELED,
						},
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				return &ProcessActivityResponse{
					Transactions: []*ussTaxPb.TransactionWrapper{createNonTradeTransaction(activity, ussTaxPb.TransactionType_TRANSACTION_TYPE_NO_OP)},
				}
			},
		},
		{
			name: "Successfully process stock split activity when there is existing suspense activity",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_NonTradeActivity{
						NonTradeActivity: &vgStocksPb.NonTradeActivity{
							Id:           "id1",
							AccountId:    "acctId",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 02, Day: 05},
							Description:  "stock split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: 10,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_EXECUTED,
						},
					},
				},
				SuspenseActivities: &SuspenseActivities{
					ExecutionTime: time.Date(2024, 02, 05, 0, 0, 0, 0, datetime.EST5EDT),
					NonTradeActivities: map[vgStocksPb.NonTradeActivityType]map[string][]*vgStocksPb.NonTradeActivity{
						vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT: {
							"APPL": []*vgStocksPb.NonTradeActivity{
								{Id: "id2"},
							},
						},
					},
				},
			},
			want: func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse {
				return &ProcessActivityResponse{
					SuspenseActivities: &SuspenseActivities{
						ExecutionTime: activity.GetExecutedAt(),
						NonTradeActivities: map[vgStocksPb.NonTradeActivityType]map[string][]*vgStocksPb.NonTradeActivity{
							vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT: {
								"APPL": []*vgStocksPb.NonTradeActivity{
									{Id: "id2"},
									activity.GetNonTradeActivity(),
								},
							},
						},
					},
				}
			},
		},
		{
			name: "Return error if this is not a stock split activity",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_NonTradeActivity{
						NonTradeActivity: &vgStocksPb.NonTradeActivity{
							Id:           "id1",
							AccountId:    "acctId",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_ACQUISITION,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 02, Day: 05},
							Description:  "stock split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: 10,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			want:    func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse { return nil },
			wantErr: true,
		},
		{
			name: "Return error for unhandled executed status",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_NonTradeActivity{
						NonTradeActivity: &vgStocksPb.NonTradeActivity{
							Id:           "id1",
							AccountId:    "acctId",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 02, Day: 05},
							Description:  "stock split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: 10,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			want:    func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse { return nil },
			wantErr: true,
		},
		{
			name: "Return error if net amount is non zero",
			req: &ProcessActivityRequest{
				Activity: &vgStocksPb.AccountActivity{
					Activity: &vgStocksPb.AccountActivity_NonTradeActivity{
						NonTradeActivity: &vgStocksPb.NonTradeActivity{
							Id:           "id1",
							AccountId:    "acctId",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 02, Day: 05},
							Description:  "stock split",
							Symbol:       "APPL",
							NetAmount:    &moneyPb.Money{CurrencyCode: "USD", Units: 10},
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: 10,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			want:    func(activity *vgStocksPb.AccountActivity) *ProcessActivityResponse { return nil },
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewStockSplitProcessor(nil)
			got, err := s.ProcessActivity(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessActivity() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want(tt.req.Activity), protocmp.Transform()); diff != "" {
				t.Errorf("ProcessActivity() diff=%v", diff)
			}
		})
	}
}

func TestStockSplitProcessor_ProcessSuspenseActivities(t *testing.T) {
	tests := []struct {
		name    string
		req     *ProcessSuspenseActivitiesRequest
		before  func(m *mockFields)
		want    func(activitiesMap map[string][]*vgStocksPb.NonTradeActivity) *ProcessSuspenseActivitiesResponse
		wantErr bool
	}{
		{
			name: "Successfully process reverse split suspense split activities",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol:           "APPL",
							Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 49, Nanos: *********},
							ExecutedAt:       timestampPb.New(time.Date(2024, 01, 05, 12, 11, 0, 0, datetime.IST)),
							TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
							Expense:          money.ZeroUSD(),
							ParentActivities: nil,
						},
						Quantity: decimal.NewFromFloat(312),
					},
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol:           "APPL",
							Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 49, Nanos: *********},
							ExecutedAt:       timestampPb.New(time.Date(2024, 01, 10, 12, 11, 0, 0, datetime.IST)),
							TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_SELL,
							Expense:          money.ZeroUSD(),
							ParentActivities: nil,
						},
						Quantity: decimal.NewFromFloat(30),
					},
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol:           "APPL",
							Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 0, Nanos: *********},
							ExecutedAt:       timestampPb.New(time.Date(2024, 01, 06, 12, 11, 0, 0, datetime.IST)),
							TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
							Expense:          money.ZeroUSD(),
							ParentActivities: nil,
						},
						Quantity: decimal.NewFromFloat(1.*********),
					},
				},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{
							Id:           "id2",
							AccountId:    "acct1",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: -283.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
						{
							Id:           "id1",
							AccountId:    "acct1",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: 9.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockCorporateActionsFetcher.EXPECT().GetCorporateAction(gomock.Any(), "APPL", &datePb.Date{Year: 2024, Month: 03, Day: 02},
					vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_REVERSE_SPLIT).Return(&ussTaxPb.CorporateAction{
					CorporateAction: &ussTaxPb.CorporateAction_ReverseSplit{
						ReverseSplit: &vgStocksPb.ReverseSplit{
							NewRate: 1,
							OldRate: 30,
							Symbol:  "APPL",
						},
					},
				}, nil)
			},
			want: func(activitiesMap map[string][]*vgStocksPb.NonTradeActivity) *ProcessSuspenseActivitiesResponse {
				splitActivities := lo.Map(activitiesMap["APPL"], func(activity *vgStocksPb.NonTradeActivity, _ int) *vgStocksPb.AccountActivity {
					return vgStocksPb.NewNonTradeAccountActivity(activity)
				})
				return &ProcessSuspenseActivitiesResponse{
					Transactions: []*ussTaxPb.TransactionWrapper{
						{
							Transaction: &ussTaxPb.Transaction{
								Symbol:           "APPL",
								Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 49, Nanos: *********},
								ExecutedAt:       timestampPb.New(time.Date(2024, 01, 05, 12, 11, 0, 0, datetime.IST)),
								TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
								Expense:          money.ZeroUSD(),
								ParentActivities: splitActivities,
							},
							Quantity: decimal.NewFromFloat(10.4),
						},
						{
							Transaction: &ussTaxPb.Transaction{
								Symbol:           "APPL",
								Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 49, Nanos: *********},
								ExecutedAt:       timestampPb.New(time.Date(2024, 01, 10, 12, 11, 0, 0, datetime.IST)),
								TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_SELL,
								Expense:          money.ZeroUSD(),
								ParentActivities: splitActivities,
							},
							Quantity: decimal.NewFromFloat(1),
						},
						{
							Transaction: &ussTaxPb.Transaction{
								Symbol:           "APPL",
								Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 0, Nanos: *********},
								ExecutedAt:       timestampPb.New(time.Date(2024, 01, 06, 12, 11, 0, 0, datetime.IST)),
								TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
								Expense:          money.ZeroUSD(),
								ParentActivities: splitActivities,
							},
							Quantity: decimal.NewFromFloat(0.*********),
						},
					},
				}
			},
		},
		{
			name: "Successfully process forward split suspense split activities",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol:           "APPL",
							Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 49, Nanos: *********},
							ExecutedAt:       timestampPb.New(time.Date(2024, 01, 05, 12, 11, 0, 0, datetime.IST)),
							TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
							Expense:          money.ZeroUSD(),
							ParentActivities: nil,
						},
						Quantity: decimal.NewFromFloat(10.4),
					},
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol:           "APPL",
							Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 0, Nanos: *********},
							ExecutedAt:       timestampPb.New(time.Date(2024, 01, 06, 12, 11, 0, 0, datetime.IST)),
							TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
							Expense:          money.ZeroUSD(),
							ParentActivities: nil,
						},
						Quantity: decimal.NewFromFloat(0.*********),
					},
				},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{
							Id:           "id1",
							AccountId:    "acct1",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: -10.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
						{
							Id:           "id2",
							AccountId:    "acct1",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: 313.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockCorporateActionsFetcher.EXPECT().GetCorporateAction(gomock.Any(), "APPL", &datePb.Date{Year: 2024, Month: 03, Day: 02},
					vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_FORWARD_SPLIT).Return(&ussTaxPb.CorporateAction{
					CorporateAction: &ussTaxPb.CorporateAction_ForwardSplit{
						ForwardSplit: &vgStocksPb.ForwardSplit{
							NewRate: 30,
							OldRate: 1,
							Symbol:  "APPL",
						},
					},
				}, nil)
			},
			want: func(activitiesMap map[string][]*vgStocksPb.NonTradeActivity) *ProcessSuspenseActivitiesResponse {
				splitActivities := lo.Map(activitiesMap["APPL"], func(activity *vgStocksPb.NonTradeActivity, _ int) *vgStocksPb.AccountActivity {
					return vgStocksPb.NewNonTradeAccountActivity(activity)
				})
				return &ProcessSuspenseActivitiesResponse{
					Transactions: []*ussTaxPb.TransactionWrapper{
						{
							Transaction: &ussTaxPb.Transaction{
								Symbol:           "APPL",
								Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 49, Nanos: *********},
								ExecutedAt:       timestampPb.New(time.Date(2024, 01, 05, 12, 11, 0, 0, datetime.IST)),
								TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
								Expense:          money.ZeroUSD(),
								ParentActivities: splitActivities,
							},
							Quantity: decimal.NewFromFloat(312),
						},
						{
							Transaction: &ussTaxPb.Transaction{
								Symbol:           "APPL",
								Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 0, Nanos: *********},
								ExecutedAt:       timestampPb.New(time.Date(2024, 01, 06, 12, 11, 0, 0, datetime.IST)),
								TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
								Expense:          money.ZeroUSD(),
								ParentActivities: splitActivities,
							},
							Quantity: decimal.NewFromFloat(1.*********),
						},
					},
				}
			},
		},
		{
			name: "Greater than expected diff in aggregate units compared to credited units should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol:           "APPL",
							Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 49, Nanos: *********},
							ExecutedAt:       timestampPb.New(time.Date(2024, 01, 05, 12, 11, 0, 0, datetime.IST)),
							TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
							Expense:          money.ZeroUSD(),
							ParentActivities: nil,
						},
						Quantity: decimal.NewFromFloat(10.4),
					},
				},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{
							Id:           "id2",
							AccountId:    "acct1",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: 313.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
						{
							Id:           "id1",
							AccountId:    "acct1",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: -10.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockCorporateActionsFetcher.EXPECT().GetCorporateAction(gomock.Any(), "APPL", &datePb.Date{Year: 2024, Month: 03, Day: 02},
					vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_FORWARD_SPLIT).Return(&ussTaxPb.CorporateAction{
					CorporateAction: &ussTaxPb.CorporateAction_ForwardSplit{
						ForwardSplit: &vgStocksPb.ForwardSplit{
							NewRate: 30,
							OldRate: 1,
							Symbol:  "APPL",
						},
					},
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Negative current units of stock should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol:           "APPL",
							Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 49, Nanos: *********},
							ExecutedAt:       timestampPb.New(time.Date(2024, 01, 05, 12, 11, 0, 0, datetime.IST)),
							TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_SELL,
							Expense:          money.ZeroUSD(),
							ParentActivities: nil,
						},
						Quantity: decimal.NewFromFloat(10.4),
					},
				},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: -10.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
						{
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: 313.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockCorporateActionsFetcher.EXPECT().GetCorporateAction(gomock.Any(), "APPL", &datePb.Date{Year: 2024, Month: 03, Day: 02},
					vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_FORWARD_SPLIT).Return(&ussTaxPb.CorporateAction{
					CorporateAction: &ussTaxPb.CorporateAction_ForwardSplit{
						ForwardSplit: &vgStocksPb.ForwardSplit{
							NewRate: 30,
							OldRate: 1,
							Symbol:  "APPL",
						},
					},
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Missing forward split corporate action should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{
					{
						Transaction: &ussTaxPb.Transaction{
							Symbol:           "APPL",
							Amount:           &moneyPb.Money{CurrencyCode: "USD", Units: 49, Nanos: *********},
							ExecutedAt:       timestampPb.New(time.Date(2024, 01, 05, 12, 11, 0, 0, datetime.IST)),
							TransactionType:  ussTaxPb.TransactionType_TRANSACTION_TYPE_BUY,
							Expense:          money.ZeroUSD(),
							ParentActivities: nil,
						},
						Quantity: decimal.NewFromFloat(10.4),
					},
				},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{
							Id:           "id1",
							AccountId:    "acct1",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: -10.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
						{
							Id:           "id2",
							AccountId:    "acct1",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: 313.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockCorporateActionsFetcher.EXPECT().GetCorporateAction(gomock.Any(), "APPL", &datePb.Date{Year: 2024, Month: 03, Day: 02},
					vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_FORWARD_SPLIT).Return(&ussTaxPb.CorporateAction{
					CorporateAction: &ussTaxPb.CorporateAction_ReverseSplit{
						ReverseSplit: &vgStocksPb.ReverseSplit{
							NewRate: 1,
							OldRate: 30,
							Symbol:  "APPL",
						},
					},
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Unexpected activity type (spinoff) should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{
							Id:           "id1",
							AccountId:    "acct1",
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPIN_OFF,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: -10.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			before:  func(m *mockFields) {},
			wantErr: true,
		},
		{
			name: "Zero qty in split activity should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{
							Id:               "id1",
							AccountId:        "acct1",
							Type:             vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPIN_OFF,
							ExecutedDate:     &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:      "split",
							Symbol:           "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{},
							Status:           vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			before:  func(m *mockFields) {},
			wantErr: true,
		},
		{
			name: "Missing reverse split corporate action should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: 10.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
						{
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: -313.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			before: func(m *mockFields) {
				m.mockCorporateActionsFetcher.EXPECT().GetCorporateAction(gomock.Any(), "APPL", &datePb.Date{Year: 2024, Month: 03, Day: 02},
					vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_REVERSE_SPLIT).Return(&ussTaxPb.CorporateAction{
					CorporateAction: &ussTaxPb.CorporateAction_ForwardSplit{
						ForwardSplit: &vgStocksPb.ForwardSplit{
							NewRate: 30,
							OldRate: 1,
							Symbol:  "APPL",
						},
					},
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "Missing forward split credit activity should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: -10.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
						{
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: -10.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			before:  func(m *mockFields) {},
			wantErr: true,
		},
		{
			name: "Missing forward split debit activity should lead to error",
			req: &ProcessSuspenseActivitiesRequest{
				Transactions: []*ussTaxPb.TransactionWrapper{},
				ActivitiesMap: map[string][]*vgStocksPb.NonTradeActivity{
					"APPL": {
						{
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: 10.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
						{
							Type:         vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_SPLIT,
							ExecutedDate: &datePb.Date{Year: 2024, Month: 03, Day: 02},
							Description:  "split",
							Symbol:       "APPL",
							DividendMetaInfo: &vgStocksPb.DividendMetaInfo{
								Qty: 10.*********,
							},
							Status: vgStocksPb.NonTradeActivityStatus_NON_TRADE_ACTIVITY_STATUS_PENDING_AT_VENDOR,
						},
					},
				},
			},
			before:  func(m *mockFields) {},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctl := gomock.NewController(t)
			defer ctl.Finish()
			m := initMocks(ctl)
			tt.before(m)
			s := NewStockSplitProcessor(m.mockCorporateActionsFetcher)
			got, err := s.ProcessSuspenseActivities(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessSuspenseActivities() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr {
				return
			}
			if diff := deep.Equal(got, tt.want(tt.req.ActivitiesMap)); len(diff) != 0 {
				t.Errorf("ProcessSuspenseActivities() got = %v,\n want %v,\n diff %v", got, tt.want(tt.req.ActivitiesMap), diff)
			}
		})
	}
}
