<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="httpgw-gen-remote-staging" type="GoApplicationRunConfiguration" factoryName="Go Application">
    <module name="gamma" />
    <working_directory value="$PROJECT_DIR$" />
    <envs>
      <env name="ENVIRONMENT" value="staging" />
      <env name="CONFIG_DIR" value="$PROJECT_DIR$/httpgw/config/values/" />
      <env name="REMOTE_DEBUG" value="enable" />
      <env name="AWS_PROFILE" value="epifi-staging" />
      <env name="AWS_SDK_LOAD_CONFIG" value="true" />
      <env name="CGO_ENABLED" value="0" />
      <env name="REMOTE_DEBUG_CONFIG" value="{&quot;consumer&quot;:&quot;disable&quot;}" />
    </envs>
    <kind value="PACKAGE" />
    <package value="github.com/epifi/gamma/cmd/servers/staging/httpgw" />
    <directory value="$PROJECT_DIR$" />
    <filePath value="$PROJECT_DIR$/cmd/servers/staging/httpgw/server.gen.go" />
    <output_directory value="$PROJECT_DIR$/output/httpgw" />
    <method v="2">
      <option name="RunConfigurationTask" enabled="true" run_configuration_name="init_httpgw_remote_debug" run_configuration_type="ShConfigurationType" />
    </method>
  </configuration>
</component>