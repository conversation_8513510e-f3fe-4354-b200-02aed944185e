package main

import (
	"context"
	"flag"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	dm "github.com/epifi/gamma/accrual/dao/model"
	"github.com/epifi/gamma/accrual/model"
	accrualPb "github.com/epifi/gamma/api/accrual"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/scripts/resolve_balance_for_expired_fi_coins/config"
)

const (
	maxAllowedPaginatedCalls = 300
	nextPageFetchWaitTime    = 2 * time.Second
	maxPageSize              = 100
)

// nolint funlen
func main() {
	flag.Parse()

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	conf, err := config.Load()
	if err != nil {
		logger.Panic("failed to load config", zap.Error(err))
	}
	// connect to accrual db
	db, err := storagev2.NewPostgresDBWithConfig(conf.AccrualDb, false)
	if err != nil {
		logger.Panic("failed to establish DB conn", zap.Error(err))
	}
	sqlDB, err := db.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	defer func() { _ = sqlDB.Close() }()

	// init accrual service client
	accrualConn := epifigrpc.NewConnByService(cfg.ACCRUAL_SERVICE)
	defer epifigrpc.CloseConn(accrualConn)
	accrualClient := accrualPb.NewAccrualClient(accrualConn)

	ctx, cancelFunc := context.WithTimeout(context.Background(), 40*time.Minute)
	defer cancelFunc()
	totalAccountsCount := 0
	successResolutionCount := 0
	failedAccountsCount := 0
	for i := 0; i < maxAllowedPaginatedCalls; i++ {
		// fetch account ids for expired balance accounts (expired fi coins)
		accrualAccounts, err := getExpiredFiCoinsAccountIds(ctx, db)
		if err != nil {
			logger.Panic("error fetching account ids which are expired", zap.Int("totalAccountsCount", totalAccountsCount), zap.Int("successfulResolutionCount", successResolutionCount), zap.Int("failureResolutionCount", failedAccountsCount), zap.Error(err))
		}
		if len(accrualAccounts) == 0 {
			logger.Info(ctx, "no more accounts with expired fi coins")
			// if no accounts are present with expired fi coins condition, stop the process.
			break
		}
		totalAccountsCount += len(accrualAccounts)
		// call resolve balance RPC for these account ids
		for _, account := range accrualAccounts {
			resp, err := accrualClient.ResolveBalance(ctx, &accrualPb.ResolveBalanceRequest{AccountId: account.AccountId})
			var rpcErr error
			if rpcErr = epifigrpc.RPCError(resp, err); rpcErr != nil {
				logger.Error(ctx, "error while resolving balance for expired fi coin account", zap.String("accountId", account.AccountId), zap.String("nextExpiryTime", account.BalanceNextExpiryTime.String()), zap.Error(rpcErr))
			}
			// success & failure resolution count
			if rpcErr != nil {
				failedAccountsCount++
			} else {
				successResolutionCount++
			}
		}

		// waiting for sometime before fetching the next page to avoid overloading the servers
		time.Sleep(nextPageFetchWaitTime)
	}
	logger.Info(ctx, "script completed successfully", zap.Int("totalAccountsCount", totalAccountsCount), zap.Int("successfulResolutionCount", successResolutionCount), zap.Int("failureResolutionCount", failedAccountsCount))
}

func getExpiredFiCoinsAccountIds(ctx context.Context, db *gorm.DB) ([]*model.Account, error) {
	currTime := time.Now()
	db = db.Model(&dm.AccrualAccount{})
	// add account_type as AccountType_ACCOUNT_TYPE_FICOINS
	db = db.Where("account_type = ?", accrualPb.AccountType_ACCOUNT_TYPE_FICOINS)
	// add expiry time as less than current time
	db = db.Where("next_expiry_time < ?", currTime)
	// available balance must be greater than zero
	db = db.Where("available_balance > ?", 0)
	// limit max accounts to get in one query to 100
	db = db.Limit(maxPageSize)
	var accrualAccounts []*dm.AccrualAccount
	res := db.Select([]string{"id", "next_expiry_time"}).Find(&accrualAccounts)
	if res.Error != nil {
		logger.Error(ctx, "failed to get accounts for expired balance accounts", zap.Error(res.Error))
		return nil, res.Error
	}
	var accounts []*model.Account
	for _, account := range accrualAccounts {
		accounts = append(accounts, account.ConvertToServiceModel())
	}
	return accounts, nil
}
