//nolint:gocritic
package lenden

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	typesPb "github.com/epifi/gamma/api/typesv2"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/consent"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFePb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	"github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/baseprovider"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	lendenPkg "github.com/epifi/gamma/pkg/loans/lenden"
)

const (
	lendenPartnershipLogo = "https://epifi-icons.pointz.in/loans/lenden/powered-by-innofin-solutions-logo.png"
	// TODO(Brijesh): Figure out with help of client devs what formatting is needed to show the text as a list of items with dashes
	pepIndianConsentText  = "By proceeding you declare that you are not a politically exposed person and you confirm that you are not a US person/citizen nor a resident for Tax purposes in any country other than India"
	kycAadhaarConsentText = "By proceeding I hereby provide my consent to obtain my Aadhaar details from UIDAI/Digilocker and share with Innofin Solutions Pvt Ltd for KYC verification to avail credit facility"
	mandateConsentText    = "I authorise Innofin Solutions Pvt Ltd to debit my bank account as per the provided mandate and bank account details. I understand the bank may apply processing charges & I can cancel or amend this mandate by informing Innofin Solutions Pvt Ltd"
)

var (
	tncTermInfos = []*deeplinkPb.TermInfo{
		{
			ConsentId:       consent.ConsentType_CONSENT_TYPE_LDC_POLITICAL_EXPOSURE_CITIZENSHIP.String(),
			TermText:        common.GetTextFromHtmlStringFontColourFontStyle(pepIndianConsentText, colors.ColorNight, common.FontStyle_BODY_4),
			IsTermClickable: false,
		},
		{
			ConsentId:       consent.ConsentType_CONSENT_TYPE_LDC_AADHAAR_DATA_PULL.String(),
			TermText:        common.GetTextFromHtmlStringFontColourFontStyle(kycAadhaarConsentText, colors.ColorNight, common.FontStyle_BODY_4),
			IsTermClickable: false,
		},
		{
			ConsentId:       consent.ConsentType_CONSENT_TYPE_LDC_PAYMENT_MANDATE.String(),
			TermText:        common.GetTextFromHtmlStringFontColourFontStyle(mandateConsentText, colors.ColorNight, common.FontStyle_BODY_4),
			IsTermClickable: false,
		},
	}
	esignTnCCheckboxes = []*widget.CheckboxItem{
		{
			Id:          consent.ConsentType_CONSENT_LOANS_LDC_KFS_AND_LOAN_AGREEMENT_UNDERSTOOD.String(),
			DisplayText: common.GetTextFromStringFontColourFontStyle("I confirm that I have read and understood the Key Fact Statement and Loan Agreement.", colors.ColorNight, common.FontStyle_BODY_4),
		},
		{
			Id:          consent.ConsentType_CONSENT_LOANS_LDC_KFS_ALLOW_LOCATION.String(),
			DisplayText: common.GetTextFromStringFontColourFontStyle("I allow my location to be shared for loan agreement purpose", colors.ColorNight, common.FontStyle_BODY_4),
		},
		{
			Id:          consent.ConsentType_CONSENT_LOANS_LDC_KFS_AND_LOAN_AGREEMENT_LANGUAGE_UNDERSTOOD.String(),
			DisplayText: common.GetTextFromStringFontColourFontStyle("I confirm the information is presented in the language I understand", colors.ColorNight, common.FontStyle_BODY_4),
		},
	}
)

type Provider struct {
	*baseprovider.BaseDeeplinkProvider
}

var _ provider.IDeeplinkProvider = &Provider{}

func NewLendenProvider(baseDeeplinkProvider *baseprovider.BaseDeeplinkProvider) *Provider {
	return &Provider{
		BaseDeeplinkProvider: baseDeeplinkProvider,
	}
}

func (ldc *Provider) GetLoanHeader() *palPbFeEnums.LoanHeader {
	return &palPbFeEnums.LoanHeader{
		LoanProgram: palPbFeEnums.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		Vendor:      palPbFeEnums.Vendor_LENDEN,
	}
}

// TODO(mohitswain): using base provider for testing, change to LDC
// nolint:dupl
func (ldc *Provider) GetLoanLandingScreenDeepLink(ctx context.Context, lh *palPbFeEnums.LoanHeader, req *provider.LandingInfoRequest) (*deeplinkPb.Deeplink, error) {
	req.DefaultLoanAmountPercentage = ldc.GetPreApprovedLoanConfig().LoanDetailsSelectionV2Flow().DefaultAmountPercentage().Get(lh.GetVendor().String())
	dl, err := ldc.BaseDeeplinkProvider.GetLoanLandingScreenV2DeepLink(ctx, lh, req)
	if err != nil {
		return nil, errors.Wrap(err, "error in getting base deeplink")
	}

	screenOptions := &preapprovedloans.LoansLandingScreenV2{}
	err = dl.GetScreenOptionsV2().UnmarshalTo(screenOptions)
	if err != nil {
		return nil, errors.Wrap(err, "error in unmarshalling deeplink")
	}

	for _, component := range screenOptions.GetComponents() {
		switch c := component.GetComponent().(type) {
		case *preapprovedloans.LoansScreenUiComponents_FooterComponent:
			c.FooterComponent.GetPartnershipComponent().PartnerLogos = []*common.VisualElement{common.GetVisualElementFromUrlHeightAndWidth(lendenPartnershipLogo, 40, 100)}
		}
	}

	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_LANDING_INFO_V2_SCREEN, screenOptions)
}

func (ldc *Provider) GetLoanOfferDetailsScreenDeepLink(ctx context.Context, actorId string, lh *palPbFeEnums.LoanHeader, od *palPb.GetOfferDetailsResponse, req *palFePb.GetOfferDetailsRequest) (*deeplinkPb.Deeplink, error) {
	if req.GetScreen() == deeplinkPb.Screen_LOANS_CUSTOM_PLAN_SELECTION_BOTTOM_SHEET {
		dl := ldc.GetOfferDetailsV3ScreenDeeplink(ctx, lh, od, req)
		return ldc.updateCustomPlanBottomSheet(od, dl)
	}
	dl := ldc.BaseDeeplinkProvider.GetOfferDetailsV3ScreenDeeplink(ctx, lh, od, req)
	loanOfferDetailsScreenOptions := &palTypesPb.LoansOfferDetailsScreenOptions{}
	if err := dl.GetScreenOptionsV2().UnmarshalTo(loanOfferDetailsScreenOptions); err != nil {
		return nil, errors.Wrap(err, "error unmarshalling to loan detail selection screen v2")
	}
	loanOfferDetailsScreenOptions.PartnerVisualElementComponent = &palTypesPb.VisualElementComponent{
		VisualElement: common.GetVisualElementFromUrlHeightAndWidth(lendenPartnershipLogo, 20, 140),
		TopMargin:     1,
	}
	loanOfferDetailsScreenOptions.GetAmountSelectionSection().StepSize = lendenPkg.MinProductAmountINRStepSize
	loanOfferDetailsScreenOptions.GetAmountSelectionSection().StepInterval = lendenPkg.MinProductAmountINRStepSize
	// nolint: gosec
	loanOfferDetailsScreenOptions.GetAmountSelectionSection().StepCount = int32(lendenPkg.GetAmountSliderNumberOfSteps(od.GetOfferInfo().GetMinLoanAmount(), od.GetOfferInfo().GetMaxLoanAmount()))
	yearlyInterestRate := math.Round(od.GetOfferInfo().GetInterestRate()*12*100) / 100
	loanOfferDetailsScreenOptions.GetLoanPlanSelectionSection().GetPlanInfoTags()[0] = ui.NewITC().WithContainerPadding(6, 12, 6, 12).WithContainerCornerRadius(30).WithContainerBackgroundColor("#FBF3E6").WithTexts(common.GetTextFromStringFontColourFontStyle(fmt.Sprintf("@ %.1f%% p.a.", yearlyInterestRate), "#313234", common.FontStyle_SUBTITLE_S))
	loanOfferDetailsScreenOptions.IsAmountNonEditable = true

	// Currently out of all plans only custom plan has a deeplink and is appended at the end of all plans,
	// hence using that as a proxy.
	loanPlanOptions := loanOfferDetailsScreenOptions.GetLoanPlanSelectionSection().GetLoanPlanOptions()
	if len(loanPlanOptions) > 0 && loanPlanOptions[len(loanPlanOptions)-1].GetDeeplink() != nil {
		customPlanBottomSheetDl := loanPlanOptions[len(loanPlanOptions)-1].GetDeeplink()
		updatedCustomPlanBottomSheetDl, err := ldc.updateCustomPlanBottomSheet(od, customPlanBottomSheetDl)
		if err != nil {
			return nil, errors.Wrap(err, "error updating custom plan bottom sheet")
		}
		loanPlanOptions[len(loanPlanOptions)-1].Deeplink = updatedCustomPlanBottomSheetDl
	}
	dl, err := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_OFFER_DETAILS_SCREEN, loanOfferDetailsScreenOptions)
	if err != nil {
		return nil, errors.Wrap(err, "error creating loan details selection screen v2 deeplink")
	}
	return dl, nil
}

func (ldc *Provider) updateCustomPlanBottomSheet(od *palPb.GetOfferDetailsResponse, dl *deeplinkPb.Deeplink) (*deeplinkPb.Deeplink, error) {
	customPlanBottomSheetOptions := &palTypesPb.LoansCustomPlanSelectionBottomSheet{}
	if err := dl.GetScreenOptionsV2().UnmarshalTo(customPlanBottomSheetOptions); err != nil {
		return nil, errors.Wrap(err, "error unmarshalling to custom plan bottom sheet options")
	}
	customPlanBottomSheetOptions.GetAmountSelectionComponent().StepSize = lendenPkg.MinProductAmountINRStepSize
	customPlanBottomSheetOptions.GetAmountSelectionComponent().StepInterval = lendenPkg.MinProductAmountINRStepSize
	// nolint: gosec
	customPlanBottomSheetOptions.GetAmountSelectionComponent().StepCount = int32(lendenPkg.GetAmountSliderNumberOfSteps(od.GetOfferInfo().GetMinLoanAmount(), od.GetOfferInfo().GetMaxLoanAmount()))
	dl, err := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_CUSTOM_PLAN_SELECTION_BOTTOM_SHEET, customPlanBottomSheetOptions)
	if err != nil {
		return nil, errors.Wrap(err, "error creating custom plan bottom sheet deeplink")
	}
	return dl, nil
}

func (ldc *Provider) GetLoanApplicationDetailsScreenDeepLink(ctx context.Context, actorId string, lh *palPbFeEnums.LoanHeader, od *palPb.GetOfferDetailsResponse) (*deeplinkPb.Deeplink, error) {
	dl, _ := ldc.BaseDeeplinkProvider.GetLoanApplicationDetailsScreenV2DeepLink(ctx, actorId, lh, od, false)
	loanApplicationDetailsScreenOptions := &palTypesPb.LoanApplicationDetailsScreen{}
	if err := dl.GetScreenOptionsV2().UnmarshalTo(loanApplicationDetailsScreenOptions); err != nil {
		return nil, errors.Wrap(err, "error unmarshalling to loan application details screen")
	}
	// TODO(Brijesh): Move away from modifiying existing deeplinks to use composition pattern properly
	loanApplicationDetailsScreenOptions.GetComponents()[3].GetTncComponent().TermInfos = tncTermInfos
	loanApplicationDetailsScreenOptions.GetComponents()[4].GetVisualElement().VisualElement = common.GetVisualElementImageFromUrl(lendenPartnershipLogo).WithProperties(&common.VisualElementProperties{Width: 140, Height: 20})
	loanApplicationDetailsScreenOptions.GetComponents()[2].GetDetails().GetItemList().GetItems()[1].Value = ui.NewITC().WithTexts(common.GetPlainStringText("1st of every month").WithFontStyle(common.FontStyle_HEADLINE_S).WithFontColor("#313234"))
	nextEmiDateString := getNextEmiDate()
	loanApplicationDetailsScreenOptions.GetComponents()[2].GetDetails().GetItemList().GetItems()[0].Value = ui.NewITC().WithTexts(common.GetPlainStringText(nextEmiDateString).WithFontStyle(common.FontStyle_HEADLINE_S).WithFontColor("#313234"))
	yearlyInterestRate := fmt.Sprintf("%.1f%%", math.Round(od.GetOfferInfo().GetInterestRate()*12*100)/100)
	loanApplicationDetailsScreenOptions.GetComponents()[1].GetDetails().GetItemList().GetItems()[0].Value = ui.NewITC().WithTexts(common.GetPlainStringText(yearlyInterestRate).WithFontStyle(common.FontStyle_HEADLINE_S).WithFontColor("#313234"))
	dl, err := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOAN_APPLICATION_DETAIL_SCREEN, loanApplicationDetailsScreenOptions)
	if err != nil {
		return nil, errors.Wrap(err, "error creating loan application details screen deeplink")
	}
	return dl, nil
}

func getNextEmiDate() string {
	timeNowInIST := time.Now().In(datetime.IST)
	delayByMonth := 0
	if timeNowInIST.Day() > 20 {
		delayByMonth += 1
	}
	nextEmiDate := datetime.TimeToDateInLoc(datetime.StartOfMonth(time.Now().AddDate(0, 1+delayByMonth, 0).In(datetime.IST)), datetime.IST)
	return datetime.DateToString(nextEmiDate, "02 Jan 2006", datetime.IST)
}

// Start: esign
func (ldc *Provider) GetEsignViewDocumentScreen(lh *palPbFeEnums.LoanHeader, loanRequestId string, documentUrl string, docType palPbFeEnums.LoanDocType) *deeplinkPb.Deeplink {
	// first page
	if docType == palPbFeEnums.LoanDocType_LOAN_DOC_TYPE_KFS {
		viewLoanAgreementDeeplink := &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_E_SIGN_VIEW_DOCUMENT_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanESignViewDocumentScreenOptions{
				PreApprovedLoanESignViewDocumentScreenOptions: &deeplinkPb.PreApprovedLoanESignViewDocumentScreenOptions{
					LoanDocType:   pal_enums.LoanDocType_LOAN_DOC_TYPE_LOAN_AGREEMENT, // next la screen type
					LoanHeader:    lh,
					LoanRequestId: loanRequestId,
					Agree: &deeplinkPb.Cta{
						Type:         deeplinkPb.Cta_CUSTOM,
						Text:         "I agree to all the details",
						DisplayTheme: deeplinkPb.Cta_PRIMARY,
						Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
					},
				},
			},
		}
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_E_SIGN_VIEW_DOCUMENT_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanESignViewDocumentScreenOptions{
				PreApprovedLoanESignViewDocumentScreenOptions: &deeplinkPb.PreApprovedLoanESignViewDocumentScreenOptions{
					Agree: &deeplinkPb.Cta{
						Type:         deeplinkPb.Cta_CUSTOM,
						Text:         "Continue",
						DisplayTheme: deeplinkPb.Cta_PRIMARY,
						Deeplink:     viewLoanAgreementDeeplink,
						Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
					},
					LoanHeader:    lh,
					DocumentUrl:   documentUrl,
					LoanRequestId: loanRequestId,
					LoanDocType:   pal_enums.LoanDocType_LOAN_DOC_TYPE_KFS, // current kfs screen type
				},
			},
		}
	}

	// second page
	cbReq := &palFePb.ClientCallbackRequest{
		Type:       palFePb.ClientCallbackRequest_E_SIGN,
		Result:     palFePb.ClientCallbackRequest_SUCCESS,
		LoanHeader: lh,
		Identifier: &palFePb.ClientCallbackRequest_LoanReqId{
			LoanReqId: loanRequestId,
		},
	}
	cbReqString, marshalErr := protojson.Marshal(cbReq)
	if marshalErr != nil {
		logger.ErrorNoCtx("error in marshalling proto message", zap.Error(marshalErr))
		return nil
	}
	// This DL will override the DL sent from backend
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_E_SIGN_VIEW_DOCUMENT_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanESignViewDocumentScreenOptions{
			PreApprovedLoanESignViewDocumentScreenOptions: &deeplinkPb.PreApprovedLoanESignViewDocumentScreenOptions{
				Agree: &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_CUSTOM,
					Text:         "I Agree",
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Deeplink: deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_PL_CLIENT_CALLBACK_SCREEN,
						&palTypesPb.ClientCallbackScreenOptions{
							LoanHeader:        lh,
							ClientCallbackReq: string(cbReqString),
						},
					),
					Status: deeplinkPb.Cta_CTA_STATUS_ENABLED,
				},
				LoanHeader:    lh,
				DocumentUrl:   documentUrl,
				LoanRequestId: loanRequestId,
				LoanDocType:   pal_enums.LoanDocType_LOAN_DOC_TYPE_LOAN_AGREEMENT, // current la screen type
				CheckBoxes:    esignTnCCheckboxes,
			},
		},
	}
}

// End:esign

func (ldc *Provider) GetLoanAccountDetailsForDashboard(ctx context.Context, lh *palPbFeEnums.LoanHeader, li *palPb.LoanInfo) (*provider.GetDashboardLoanAccountDetailsResponse, error) {

	baseDl, baseDlErr := ldc.BaseDeeplinkProvider.GetLoanAccountDetailsForDashboard(ctx, lh, li)
	if baseDlErr != nil {
		return nil, fmt.Errorf("error in getting baseDl from mv, err: %w", baseDlErr)
	}
	if baseDl == nil {
		return nil, nil
	}

	return baseDl, nil
}
func (ldc *Provider) GetLoanApplicationDetailsForDashboard(ctx context.Context, lh *palPbFeEnums.LoanHeader, lr *palPb.LoanRequest, lses []*palPb.LoanStepExecution, loanOffer *palPb.LoanOffer) (*provider.GetDashboardLoanApplicationDetailsResponse, error) {
	baseDl, baseDlErr := ldc.BaseDeeplinkProvider.GetLoanApplicationDetailsForDashboard(ctx, lh, lr, lses, loanOffer)
	if baseDlErr != nil {
		return nil, fmt.Errorf("error in getting baseDl from abfl, err: %w", baseDlErr)
	}
	if baseDl == nil {
		return nil, nil
	}

	loanAmountString := moneyPkg.ToDisplayStringInIndianFormat(lr.GetDetails().GetLoanInfo().GetAmount(), 0, false)
	if loanAmountString == "" {
		loanAmountString = "50k-5 lakhs"
	}

	for i := range baseDl.GetActiveApplicationCards().GetComponents() {
		if baseDl.GetActiveApplicationCards().GetComponents()[i].GetCardWithLineProgress() != nil {
			baseDl.GetActiveApplicationCards().GetComponents()[i].GetCardWithLineProgress().Rows = []*palTypesPb.CardWithLineProgress_Row{
				{
					LeftItem: &palTypesPb.CardWithLineProgress_ColumnItem{
						Item: &palTypesPb.CardWithLineProgress_ColumnItem_VerticalTitleValuePair{VerticalTitleValuePair: &palTypesPb.VerticalTitleValuePair{
							Title: &ui.IconTextComponent{Texts: []*common.Text{provider.GetText("Loan Amount", "#929599", common.FontStyle_HEADLINE_XS)}},
							Value: ui.NewITC().WithTexts(common.GetTextFromStringFontColourFontStyle("₹ ", colors.ColorSlate, common.FontStyle_HEADLINE_XS),
								common.GetTextFromStringFontColourFontStyle(loanAmountString, colors.ColorDarkLayer2, common.FontStyle_NUMBER_L))}},
					},
					RightItem: getLoanApplicationDashboardRightVisualElement(lh, lr, lses, loanOffer),
				},
			}

			if lses[0].GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ROI_MODIFICATION {
				baseDl.GetActiveApplicationCards().GetComponents()[i].GetCardWithLineProgress().CtaBottomRow = nil
				baseDl.GetActiveApplicationCards().GetComponents()[i].GetCardWithLineProgress().BannerWithStageProgress = &palTypesPb.CardWithLineProgress_BannerWithStageProgress{
					BgColor:             "#FFFFFF",
					Title:               nil,
					SectionTypeProgress: baseprovider.GetLoanDisbursalBannerForDashboard(loanAmountString, lr, lses[0]).GetCardWithStageProgress().GetStageProgress()}
			}
			if baseDl.GetActiveApplicationCards().GetComponents()[i].GetCardWithLineProgress().GetCtaTitleRow().GetRightHamburgerComponent() != nil {
				baseDl.GetActiveApplicationCards().GetComponents()[i].GetCardWithLineProgress().GetCtaTitleRow().RightHamburgerComponent = nil
			}
		}
	}
	return baseDl, nil
}

func GetText(text string, color string, style common.FontStyle) *common.Text {
	return &common.Text{
		DisplayValue: &common.Text_PlainString{
			PlainString: text,
		},
		FontColor: color,
		FontStyle: &common.Text_StandardFontStyle{
			StandardFontStyle: style,
		},
	}
}

func getLoanApplicationDashboardRightVisualElement(lh *palPbFeEnums.LoanHeader, lr *palPb.LoanRequest, lses []*palPb.LoanStepExecution, _ *palPb.LoanOffer) *palTypesPb.CardWithLineProgress_ColumnItem {
	isModificationInInterestRateAllowed := false

	var kfsLse, roiModificationLse *palPb.LoanStepExecution
	for _, lse := range lses {
		if lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS {
			kfsLse = lse
		}
		if lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ROI_MODIFICATION {
			roiModificationLse = lse
		}
	}

	if kfsLse == nil || roiModificationLse == nil {
		isModificationInInterestRateAllowed = false
	}

	// If ROI mod deadline is not set
	if kfsLse.GetDetails().GetESignStepData().GetRoiModificationDeadline() == nil ||
		kfsLse.GetDetails().GetESignStepData().GetRoiModificationDeadline().AsTime().Equal(time.Unix(0, 0)) {
		isModificationInInterestRateAllowed = false
	}

	// If ROI mod deadline is in the past
	if kfsLse.GetDetails().GetESignStepData().GetRoiModificationDeadline().AsTime().After(time.Now()) &&
		roiModificationLse.GetStatus() != palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS &&
		roiModificationLse.GetDetails().GetRoiModificationData().GetChosenRoi() == 0 {
		isModificationInInterestRateAllowed = true
	}

	if !isModificationInInterestRateAllowed {
		return &palTypesPb.CardWithLineProgress_ColumnItem{
			Item: &palTypesPb.CardWithLineProgress_ColumnItem_VerticalTitleValuePair{VerticalTitleValuePair: &palTypesPb.VerticalTitleValuePair{
				Title: &ui.IconTextComponent{Texts: []*common.Text{provider.GetText("Duration", "#929599", common.FontStyle_HEADLINE_XS)}},
				Value: ui.NewITC().WithTexts(common.GetTextFromStringFontColourFontStyle(fmt.Sprintf("%d months", lr.GetDetails().GetLoanInfo().GetTenureInMonths()),
					colors.ColorDarkLayer2, common.FontStyle_NUMBER_L)),
			}},
		}
	}

	annualInterestRate := math.Round(lr.GetDetails().GetLoanInfo().GetInterestRate()*12*100) / 100
	return &palTypesPb.CardWithLineProgress_ColumnItem{
		Item: &palTypesPb.CardWithLineProgress_ColumnItem_VerticalTitleValuePair{VerticalTitleValuePair: &palTypesPb.VerticalTitleValuePair{
			Title: &ui.IconTextComponent{
				Texts: []*common.Text{provider.GetText("Interest", "#929599", common.FontStyle_HEADLINE_XS)},
			},
			Value: ui.NewITC().WithTexts(common.GetTextFromStringFontColourFontStyle(fmt.Sprintf("%.2f%% p.a.", annualInterestRate),
				colors.ColorDarkLayer2, common.FontStyle_NUMBER_L)).
				WithDeeplink(deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_OPTION_SELECTION_FULL_SCREEN, &palTypesPb.LoansOptionSelectionScreenOption{
					PartnerLogo: common.GetVisualElementFromUrlHeightAndWidth(lendenPartnershipLogo, 40, 100),
					OptionView:  getAllowedModifyRoiList(roiModificationLse.GetDetails().GetRoiModificationData().GetAllowedRoiForModification(), lr.GetDetails().GetLoanInfo().GetInterestRate()),
					ToolbarRightCta: ui.NewITC().WithLeftImageUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/grey_question_info_icon.png", 16, 16).WithTexts(common.GetTextFromStringFontColourFontStyle("Get help", "#6A6D70", common.FontStyle_HEADLINE_M)).WithDeeplink(&deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_HELP_MAIN,
					}).WithContainerPadding(4, 17, 4, 8).WithContainerBackgroundColor("#FFFFFF").WithContainerCornerRadius(16),
					PrimaryCta: &deeplinkPb.Cta{
						Type: deeplinkPb.Cta_CUSTOM,
						Text: "Save Changes",
						Deeplink: deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_CONFIRMATION_BOTTOM_SHEET, &palTypesPb.LoansConfirmationBottomSheetScreenOption{
							Title:         GetText("Change EMI plan?", "#282828", common.FontStyle_SUBTITLE_2),
							Subtitle:      GetText("This might affect your chances of securing a loan as per our lending partner's policies", "#282828", common.FontStyle_BODY_XS),
							LoanRequestId: lr.GetId(),
							LoanHeader:    lh,
							BgColor:       "#E6E9ED",
							ConfirmCta: &deeplinkPb.Cta{
								Type:         deeplinkPb.Cta_CUSTOM,
								DisplayTheme: deeplinkPb.Cta_SECONDARY,
								Text:         "Change EMI",
							},
							CancelCta: &deeplinkPb.Cta{
								Type:         deeplinkPb.Cta_CUSTOM,
								DisplayTheme: deeplinkPb.Cta_PRIMARY,
								Text:         "Cancel",
							},
							ConfirmationLogo: common.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/lending/confirmation_logo.png", 20, 20),
						},
						),
					},
					LoanRequestId: lr.GetId(),
					LoanHeader:    lh,
				})).WithRightVisualElement(common.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/preapprovedloan/edit_logo.png", 20, 20)),
		}},
	}
}

func getAllowedModifyRoiList(modifyRoiList []float64, selectedInterestRate float64) *ui.OptionSelectionView {
	var optionItems []*ui.OptionSelectionItem
	for ind, val := range modifyRoiList {
		if selectedInterestRate != val {
			annualInterestRate := math.Round(val*12*100) / 100
			optionItems = append(optionItems, &ui.OptionSelectionItem{
				Id:         int64(ind),
				Identifier: fmt.Sprintf("%f", val),
				OptionValue: &ui.IconTextComponent{
					Texts: []*common.Text{
						GetText(fmt.Sprintf("@ %.2f%% p.a.", annualInterestRate), "#646464", common.FontStyle_SUBTITLE_3),
					},
				},
			})
		}
	}
	selectedAnnualInterestRate := math.Round(selectedInterestRate*12*100) / 100
	return &ui.OptionSelectionView{
		Header: &ui.OptionSelectionHeader{
			Label: GetText("Current interest rate", "#929599", common.FontStyle_SUBTITLE_2),
			Value: GetText(fmt.Sprintf("@ %.2f%% p.a.", selectedAnnualInterestRate), "#313234", common.FontStyle_SUBTITLE_2),
		},
		Items: optionItems,
	}
}

func (l *Provider) GetLoanRepaymentMethodsScreen(ctx context.Context, lh *palPbFeEnums.LoanHeader, beRes *palPb.GetPrePayDetailsResponse, amount *typesPb.Money, repayDetailsType palPbFeEnums.RepaymentDetailsType, shouldFetchUserAccounts, isPrePayViaPGEnabled bool) (*deeplinkPb.Deeplink, error) {
	dl, err := l.BaseDeeplinkProvider.GetLoanRepaymentMethodsScreen(ctx, lh, beRes, amount, repayDetailsType, shouldFetchUserAccounts, false)
	if err != nil {
		return nil, errors.Wrap(err, "error in getting base loan repayment deeplink")
	}

	screenOptions := &preapprovedloans.LoansRepaymentMethodsScreenOptions{}
	err = dl.GetScreenOptionsV2().UnmarshalTo(screenOptions)
	if err != nil {
		return nil, errors.Wrap(err, "error in unmarshalling deeplink")
	}

	screenOptions.SkipPinSetCheck = true
	screenOptions.ChoosePaymentMethodBottomContainer = &palTypesPb.LoansRepaymentMethodsScreenOptions_ChoosePaymentMethodBottomContainer{
		Title: &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetTextFromStringFontColourFontStyle("Generate payment link via lender", "#313234", common.FontStyle_HEADLINE_M),
			},
		},
		Slider: &deeplinkPb.Button{
			Text: common.GetTextFromStringFontColourFontStyle("Pay "+moneyPkg.ToDisplayString(amount.GetBeMoney()), "#FFFFFF", common.FontStyle_BUTTON_M),
			Cta: &deeplinkPb.Cta{
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
			},
		},
	}

	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_REPAYMENT_METHODS_SCREEN, screenOptions)
}
