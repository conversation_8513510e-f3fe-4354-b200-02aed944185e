package ui

import (
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"

	fePayTxnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	"github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"
	tieringPb "github.com/epifi/gamma/api/typesv2/tiering"
	"github.com/epifi/gamma/frontend/tiering/events"

	"github.com/samber/lo"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/tiering/amb/models"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

// AMBScreenBuilder defines the interface for building AMB screen
type AMBScreenBuilder interface {
	// BuildScreen constructs the complete AMB screen
	BuildScreen(data *models.AMBScreenData) *sections.Section
}

// AMBScreenBuilderImpl implements the AMBScreenBuilder interface
type AMBScreenBuilderImpl struct {
	config *genconf.Config
}

// NewAMBScreenBuilder creates a new AMBScreenBuilderImpl
func NewAMBScreenBuilder(config *genconf.Config) AMBScreenBuilder {
	return &AMBScreenBuilderImpl{
		config: config,
	}
}

// BuildScreen constructs the complete AMB screen
func (b *AMBScreenBuilderImpl) BuildScreen(data *models.AMBScreenData) *sections.Section {
	// Build all sections
	var amountNeededSection *components.Component
	var announcementSection *components.Component

	// Create announcement section if previous tier is set
	if data.ShouldShowAnnouncementSection {
		announcementSection = b.createAnnouncementSection(data)
	}

	// Create progress section
	progressSection := b.createProgressSection(data)

	// Create tier badge section
	tierBadgeSection := b.createTierBadgeSection(data)

	// Create amount needed section if user needs to add balance and shortfall is achievable
	if data.ShouldShowAmountNeededSection {
		amountNeededSection = b.createAmountNeededSection(data)
	}

	// Create banner section
	bannerSection := b.createBannerSection(data)

	// Create AMB history section
	ambHistorySection := b.createAMBHistorySection(data)

	// Create learning center section
	learningCenterSection := b.createLearningCenterSection(data)

	// Create plus and progress section
	plusAndProgressSection := &components.Component{
		Content: GetAnyWithoutError(&sections.DepthWiseListSection{
			Components: []*components.Component{
				progressSection,
				tierBadgeSection,
			},
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							Margin: &properties.PaddingProperty{
								Bottom: 12,
							},
						},
					},
				},
			},
			Alignment: sections.DepthWiseListSection_TOP_CENTER,
		}),
	}

	// Create main content section
	mainContentSection := &components.Component{
		Content: GetAnyWithoutError(&sections.VerticalListSection{
			Components: []*components.Component{
				amountNeededSection,
				bannerSection,
				ambHistorySection,
				learningCenterSection,
			},
		}),
	}

	// Combine all sections
	return &sections.Section{
		Content: &sections.Section_VerticalListSection{
			VerticalListSection: &sections.VerticalListSection{
				Components: []*components.Component{
					announcementSection,
					{Content: GetAnyWithoutError(&components.Spacer{
						SpacingValue: components.Spacing_SPACING_S,
					})},
					plusAndProgressSection,
					mainContentSection,
				},
				IsScrollable: true,
				LoadBehavior: &behaviors.LifecycleBehavior{
					Behavior: &behaviors.LifecycleBehavior_LoadBehavior{},
					AnalyticsEvent: &analytics.AnalyticsEvent{
						EventName:  events.EventAMBVisibilityScreenLoaded,
						Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{}),
					},
				},
			},
		},
	}
}

// createBannerSection builds the banner section of the AMB screen
func (b *AMBScreenBuilderImpl) createBannerSection(data *models.AMBScreenData) *components.Component {
	var (
		innerBannerSection *commontypes.VisualElement
		dl                 *deeplink.Deeplink
	)
	switch {
	case data.DynamicBanner != nil:
		innerBannerSection = data.DynamicBanner.Image
		dl = data.DynamicBanner.Deeplink
	case data.CurrentTier == external.Tier_TIER_FI_REGULAR:
		innerBannerSection, dl = b.getRegularTierBannerAndDL()
	default:
		return nil
	}

	// Add rounded corners
	innerBannerComponent := &sections.HorizontalListSection{
		Components: []*components.Component{
			{
				Content: GetAnyWithoutError(innerBannerSection),
			},
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Corner: &properties.CornerProperty{
							TopLeftCornerRadius:  12,
							TopRightCornerRadius: 12,
							BottomLeftCorner:     12,
							BottomRightCorner:    12,
						},
					},
				},
			},
		},
		InteractionBehaviors: []*behaviors.InteractionBehavior{
			{
				Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
					OnClickBehavior: &behaviors.OnClickBehavior{
						Action: GetAnyWithoutError(dl),
					},
				},
				AnalyticsEvent: &analytics.AnalyticsEvent{
					EventName: events.EventAMBVisibilityScreenActioned,
					Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{
						Component:    "banner",
						SubComponent: "promoBanner",
						ActionType:   "click",
						ScreenName:   deeplink.Screen_AMB_DETAILS_SCREEN.String(),
					}),
				},
			},
		},
	}

	colors := b.config.Tiering().AMBScreen().Colors()
	dimensions := b.config.Tiering().AMBScreen().Dimensions()

	// Return the banner section wrapped in a white container that fills the screen width
	return &components.Component{
		Content: GetAnyWithoutError(&sections.VerticalListSection{
			Components: []*components.Component{
				{
					Content: GetAnyWithoutError(innerBannerComponent),
				},
			},
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							BgColor: widget.GetBlockBackgroundColour(colors.White()),
							Size: &properties.Size{
								Width: &properties.Size_Dimension{
									Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
								},
							},
							Padding: &properties.PaddingProperty{
								Top:   dimensions.Margin(),
								Left:  16,
								Right: 16,
							},
						},
					},
				},
			},
			VisibleBehavior: &behaviors.LifecycleBehavior{
				Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
				AnalyticsEvent: &analytics.AnalyticsEvent{
					EventName: events.EventAMBSectionVisibleOnScreen,
					Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{
						Component: "bannerSection",
					}),
				},
			},
		}),
	}
}

func (b *AMBScreenBuilderImpl) getRegularTierBannerAndDL() (*commontypes.VisualElement, *deeplink.Deeplink) {
	metaData := &tieringPb.TieringScreenMetaData{
		Metadata: &tieringPb.TieringScreenMetaData_PlansV2Metadata{
			PlansV2Metadata: &tieringPb.PlansV2Metadata{
				TierToFocus: external.Tier_TIER_FI_PLUS.String(),
			},
		},
	}
	metaDataMarshalled, metaDataMarshalErr := protojson.Marshal(metaData)
	if metaDataMarshalErr != nil {
		return nil, nil
	}

	dl := &deeplink.Deeplink{
		Screen: deeplink.Screen_TIER_ALL_PLANS_SCREEN_V2,
		ScreenOptionsV2: GetAnyWithoutError(
			&tiering.AllPlansV2ScreenOptions{
				MetaData: string(metaDataMarshalled),
			},
		),
	}

	return &commontypes.VisualElement{
		Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{
					Url: "https://epifi-icons.pointz.in/tiering/ambPlusBanner.png",
				},
				Properties: &commontypes.VisualElementProperties{
					Width:  -1, // Full width
					Height: 124,
				},
			},
		},
	}, dl
}

// createAnnouncementSection builds the announcement section for tier change
func (b *AMBScreenBuilderImpl) createAnnouncementSection(data *models.AMBScreenData) *components.Component {
	return &components.Component{
		Content: GetAnyWithoutError(&sections.HorizontalListSection{
			Components: []*components.Component{
				{
					Content: GetAnyWithoutError(&commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: b.config.Tiering().AMBScreen().Images().Announcement(),
								},
								Properties: &commontypes.VisualElementProperties{
									Height: 24,
									Width:  24,
								},
							},
						},
					}),
				},
				{
					Content: GetAnyWithoutError(
						GetTextFromStringFontColourFontStyle(fmt.Sprintf("You moved from %s to %s on %s. New AMB tracking started.", data.PreviousTierDisplayName, data.CurrentTierDisplayName, data.TierChangeDate), b.config.Tiering().AMBScreen().Colors().Text(), commontypes.FontStyle_NUMBER_XS, commontypes.Text_ALIGNMENT_LEFT)),
				},
			},
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							Padding: &properties.PaddingProperty{
								Top:    16,
								Bottom: 16,
								Left:   16,
								Right:  16,
							},
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#E8F3F7",
								},
							},
							Corner: &properties.CornerProperty{
								TopLeftCornerRadius:  12,
								TopRightCornerRadius: 12,
								BottomLeftCorner:     12,
								BottomRightCorner:    12,
							},
						},
					},
				},
			},
			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
			ListElementOverlapProps: &properties.ListElementOverlapProps{
				OverlapDevicePixels: -12,
			},
			VisibleBehavior: &behaviors.LifecycleBehavior{
				Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
				AnalyticsEvent: &analytics.AnalyticsEvent{
					EventName: events.EventAMBSectionVisibleOnScreen,
					Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{
						Component: "announcement",
					}),
				},
			},
		}),
	}
}

// createTierBadgeSection builds the tier badge section
func (b *AMBScreenBuilderImpl) createTierBadgeSection(data *models.AMBScreenData) *components.Component {
	// Get config values
	colors := b.config.Tiering().AMBScreen().Colors()
	dimensions := b.config.Tiering().AMBScreen().Dimensions()

	return &components.Component{
		Content: GetAnyWithoutError(&ui.IconTextComponent{
			Texts: []*commontypes.Text{
				GetTextFromStringFontColourFontStyle(data.CurrentTierDisplayName, colors.White(), commontypes.FontStyle_SUBTITLE_M, commontypes.Text_ALIGNMENT_LEFT),
			},
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(data.TierBadgeImageURL, 26, 26),
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BackgroundColour: data.TierBadgeGradient,
				BgBorderColour: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{
						BlockColour: b.config.Tiering().AMBScreen().Colors().White(),
					},
				},
				BorderWidth:   1,
				RightPadding:  dimensions.Padding(),
				LeftPadding:   dimensions.Padding(),
				TopPadding:    8,
				BottomPadding: 8,
				CornerRadius:  20,
			},
		}),
	}
}

// nolint:dupl
// createAmountNeededSection builds the amount needed section
func (b *AMBScreenBuilderImpl) createAmountNeededSection(data *models.AMBScreenData) *components.Component {
	// Get config values
	colors := b.config.Tiering().AMBScreen().Colors()
	dimensions := b.config.Tiering().AMBScreen().Dimensions()
	textContent := b.config.Tiering().AMBScreen().TextContent()

	return &components.Component{
		Content: GetAnyWithoutError(&sections.VerticalListSection{
			Components: []*components.Component{
				CreateText(data.AmountStatusText, colors.Subtitle(), commontypes.FontStyle_SUBTITLE_S),
				{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_XS})},
				{
					Content: GetAnyWithoutError(&ui.IconTextComponent{
						Texts: []*commontypes.Text{
							GetTextFromStringFontColourFontStyle(formatCurrency(data.AmountNeeded), data.AmountNeededColor, commontypes.FontStyle_HEADLINE_2XL, commontypes.Text_ALIGNMENT_LEFT),
						},
						RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(b.config.Tiering().AMBScreen().Images().InfoGreen(), 16, 16),
						RightImgTxtPadding: 12,
					}),
					InteractionBehaviors: []*behaviors.InteractionBehavior{
						{
							Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
								OnClickBehavior: &behaviors.OnClickBehavior{
									Action: GetAnyWithoutError(&deeplink.Deeplink{
										Screen: deeplink.Screen_SDUI_BOTTOM_SHEET,
										ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(&pkg.SduiBottomSheetOptions{
											Section: b.GetAMBBottomSheetSection(b.config, data, models.BALANCE_REQUIRED),
										}),
									}),
								},
							},
							AnalyticsEvent: &analytics.AnalyticsEvent{
								EventName: events.EventAMBVisibilityScreenActioned,
								Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{
									Component:    "infoIcon",
									SubComponent: "balanceInfo",
									ActionType:   "click",
									ScreenName:   deeplink.Screen_SDUI_BOTTOM_SHEET.String(),
								}),
							},
						},
					},
				},
				{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_XS})},
				b.createAddMoneyButton(textContent.AddMoneyButtonText(), colors.Primary(), colors.White(), 12, 80, &deeplink.Deeplink{
					Screen: deeplink.Screen_TRANSFER_IN,
					ScreenOptions: &deeplink.Deeplink_TransferInScreenOptions{
						TransferInScreenOptions: &deeplink.TransferInScreenOptions{
							CustomAmount: typesv2.GetFromBeMoney(data.AmountNeeded),
							UiEntryPoint: fePayTxnPb.UIEntryPoint_AMB_DETAILS.String(),
						},
					},
				}, data, deeplink.Screen_AMB_DETAILS_SCREEN, nil),
				{Content: GetAnyWithoutError(&components.Spacer{
					SpacingValue: components.Spacing_SPACING_M,
				})},
				{
					Content: GetAnyWithoutError(&sections.VerticalListSection{
						Components: []*components.Component{
							{
								Content: GetAnyWithoutError(&ui.IconTextComponent{
									Texts: []*commontypes.Text{
										GetTextFromStringFontColourFontStyle(textContent.WhatsAMBText(), colors.Primary(), commontypes.FontStyle_BUTTON_S, commontypes.Text_ALIGNMENT_LEFT),
									},
									LeftImgTxtPadding: 4,
									LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(b.config.Tiering().AMBScreen().Images().AnnouncementGreen(), 20, 20),
								}),
							},
						},
						InteractionBehaviors: []*behaviors.InteractionBehavior{
							{
								Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
									OnClickBehavior: &behaviors.OnClickBehavior{
										Action: GetAnyWithoutError(&deeplink.Deeplink{
											Screen: deeplink.Screen_SDUI_BOTTOM_SHEET,
											ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(&pkg.SduiBottomSheetOptions{
												Section: b.GetAMBBottomSheetSection(b.config, data, models.AMB_SUMMARY),
											}),
										}),
									},
								},
								AnalyticsEvent: &analytics.AnalyticsEvent{
									EventName: events.EventAMBVisibilityScreenActioned,
									Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{
										Component:    "infoText",
										SubComponent: "WhatsAMB",
										ActionType:   "click",
										ScreenName:   deeplink.Screen_SDUI_BOTTOM_SHEET.String(),
									}),
								},
							},
						},
					}),
				},
			},
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							BgColor: widget.GetBlockBackgroundColour(colors.White()),
							Padding: &properties.PaddingProperty{
								Top:    24,
								Bottom: 24,
								Left:   dimensions.Padding(),
								Right:  dimensions.Padding(),
							},
							Size: &properties.Size{
								Width: &properties.Size_Dimension{
									Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
								},
							},
							Corner: &properties.CornerProperty{
								TopLeftCornerRadius:  dimensions.CornerRadius(),
								TopRightCornerRadius: dimensions.CornerRadius(),
							},
							Margin: &properties.PaddingProperty{
								Top:    dimensions.Margin(),
								Bottom: 4,
							},
						},
					},
				},
			},
			VisibleBehavior: &behaviors.LifecycleBehavior{
				Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
				AnalyticsEvent: &analytics.AnalyticsEvent{
					EventName: events.EventAMBSectionVisibleOnScreen,
					Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{
						Component: "shortfallAmountSection",
					}),
				},
			},
		}),
	}
}

// nolint:dupl
// createProgressSection builds the progress section
func (b *AMBScreenBuilderImpl) createProgressSection(data *models.AMBScreenData) *components.Component {
	// Get config values
	colors := b.config.Tiering().AMBScreen().Colors()
	dimensions := b.config.Tiering().AMBScreen().Dimensions()
	textContent := b.config.Tiering().AMBScreen().TextContent()

	var componentsList []*components.Component

	if data.ShouldShowProgressBar {
		componentsList = []*components.Component{
			{
				Content: GetAnyWithoutError(&sections.VerticalListSection{
					Components: []*components.Component{
						{
							Content: GetAnyWithoutError(&components.LinearProgressBar{
								Progress:      data.ProgressPercentage,
								ProgressColor: widget.GetBlockBackgroundColour(data.ProgressBarColor),
								BarProperties: &properties.ContainerProperty{
									Size: &properties.Size{
										Height: &properties.Size_Dimension{
											Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
										},
									},
									BgColor: &widget.BackgroundColour{
										Colour: &widget.BackgroundColour_BlockColour{
											BlockColour: colors.Track(),
										},
									},
									Corner: &properties.CornerProperty{
										TopLeftCornerRadius:  20,
										TopRightCornerRadius: 20,
										BottomLeftCorner:     20,
										BottomRightCorner:    20,
									},
								},
							}),
						},
					},
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Padding: &properties.PaddingProperty{
										Left:   dimensions.Padding(),
										Right:  dimensions.Padding(),
										Bottom: 8,
									},
									Size: &properties.Size{
										Height: &properties.Size_Dimension{
											Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
											ExactValue: 16,
										},
									},
								},
							},
						},
					},
					VisibleBehavior: &behaviors.LifecycleBehavior{
						Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
						AnalyticsEvent: &analytics.AnalyticsEvent{
							EventName: events.EventAMBSectionVisibleOnScreen,
							Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{
								Component:    "progressSection",
								SubComponent: "progressBar",
							}),
						},
					},
				}),
			},
			{
				Content: GetAnyWithoutError(&sections.HorizontalListSection{
					Components: []*components.Component{
						{
							Content: GetAnyWithoutError(&sections.VerticalListSection{
								Components: []*components.Component{
									{
										Content: GetAnyWithoutError(&ui.IconTextComponent{
											RightVisualElement: &commontypes.VisualElement{
												Asset: &commontypes.VisualElement_Image_{
													Image: &commontypes.VisualElement_Image{
														Source: &commontypes.VisualElement_Image_Url{
															Url: b.config.Tiering().AMBScreen().Images().InfoGreen(),
														},
														Properties: &commontypes.VisualElementProperties{
															Height: 13,
															Width:  13,
														},
													},
												},
											},
											RightImgTxtPadding: 4,
											Texts: []*commontypes.Text{
												GetTextFromStringFontColourFontStyle(textContent.CurrentAMBText(), colors.Subtle(), commontypes.FontStyle_SUBTITLE_S, commontypes.Text_ALIGNMENT_LEFT),
											},
										}),
										InteractionBehaviors: []*behaviors.InteractionBehavior{
											{
												Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
													OnClickBehavior: &behaviors.OnClickBehavior{
														Action: GetAnyWithoutError(&deeplink.Deeplink{
															Screen: deeplink.Screen_SDUI_BOTTOM_SHEET,
															ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(&pkg.SduiBottomSheetOptions{
																Section: b.GetAMBBottomSheetSection(b.config, data, models.CURRENT_AMB),
															}),
														}),
													},
												},
												AnalyticsEvent: &analytics.AnalyticsEvent{
													EventName: events.EventAMBVisibilityScreenActioned,
													Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{
														Component:    "infoIcon",
														SubComponent: "CurrentAMB",
														ActionType:   "click",
														ScreenName:   deeplink.Screen_SDUI_BOTTOM_SHEET.String(),
													}),
												},
											},
										},
									},
									{
										Content: GetAnyWithoutError(&ui.IconTextComponent{
											Texts: []*commontypes.Text{
												GetTextFromStringFontColourFontStyle(formatCurrency(data.CurrentAMB), data.CurrentAMBColor, commontypes.FontStyle_HEADLINE_L, commontypes.Text_ALIGNMENT_LEFT),
											},
										}),
									},
								},
								HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
							}),
						},
						{Content: GetAnyWithoutError(&components.Spacer{
							Weight: &components.Spacer_Weight{
								Value: 100,
							},
						})},
						{
							Content: GetAnyWithoutError(&sections.VerticalListSection{
								Components: []*components.Component{
									{
										Content: GetAnyWithoutError(&ui.IconTextComponent{
											RightVisualElement: &commontypes.VisualElement{
												Asset: &commontypes.VisualElement_Image_{
													Image: &commontypes.VisualElement_Image{
														Source: &commontypes.VisualElement_Image_Url{
															Url: b.config.Tiering().AMBScreen().Images().InfoGreen(),
														},
														Properties: &commontypes.VisualElementProperties{
															Height: 13,
															Width:  13,
														},
													},
												},
											},
											RightImgTxtPadding: 4,
											Texts: []*commontypes.Text{
												GetTextFromStringFontColourFontStyle(textContent.TargetAMBText(), colors.Subtle(), commontypes.FontStyle_SUBTITLE_S, commontypes.Text_ALIGNMENT_LEFT),
											},
										}),
										InteractionBehaviors: []*behaviors.InteractionBehavior{
											{
												Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
													OnClickBehavior: &behaviors.OnClickBehavior{
														Action: GetAnyWithoutError(&deeplink.Deeplink{
															Screen: deeplink.Screen_SDUI_BOTTOM_SHEET,
															ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(&pkg.SduiBottomSheetOptions{
																Section: b.GetAMBBottomSheetSection(b.config, data, models.TARGET_AMB),
															}),
														}),
													},
												},
												AnalyticsEvent: &analytics.AnalyticsEvent{
													EventName: events.EventAMBVisibilityScreenActioned,
													Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{
														Component:    "infoIcon",
														SubComponent: "TargetAMB",
														ActionType:   "click",
														ScreenName:   deeplink.Screen_SDUI_BOTTOM_SHEET.String(),
													}),
												},
											},
										},
									},
									{
										Content: GetAnyWithoutError(&ui.IconTextComponent{
											Texts: []*commontypes.Text{
												GetTextFromStringFontColourFontStyle(formatCurrency(data.TargetAMB), colors.Text(), commontypes.FontStyle_HEADLINE_L, commontypes.Text_ALIGNMENT_LEFT),
											},
										}),
									},
								},
								HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_RIGHT,
							}),
						},
					},
					HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Padding: &properties.PaddingProperty{
										Left:   dimensions.Padding(),
										Right:  dimensions.Padding(),
										Bottom: dimensions.Padding(),
									},
								},
							},
						},
					},
				}),
			},
			{
				Content: GetAnyWithoutError(&sections.HorizontalListSection{
					Components: []*components.Component{
						{
							Content: GetAnyWithoutError(&ui.IconTextComponent{
								Texts: []*commontypes.Text{
									GetTextFromStringFontColourFontStyle(data.StatusBannerText, colors.White(), commontypes.FontStyle_SUBTITLE_S, commontypes.Text_ALIGNMENT_LEFT),
								},
								LeftImgTxtPadding: 4,
								LeftVisualElement: &commontypes.VisualElement{
									Asset: &commontypes.VisualElement_Image_{
										Image: &commontypes.VisualElement_Image{
											Source: &commontypes.VisualElement_Image_Url{
												Url: data.StatusIconURL,
											},
											Properties: &commontypes.VisualElementProperties{
												Height: 22,
												Width:  22,
											},
										},
									},
								},
							}),
						},
					},
					HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									BgColor: &widget.BackgroundColour{
										Colour: &widget.BackgroundColour_BlockColour{
											BlockColour: data.StatusBannerColor,
										},
									},
									Corner: &properties.CornerProperty{
										BottomLeftCorner:  dimensions.CornerRadius(),
										BottomRightCorner: dimensions.CornerRadius(),
									},
									Size: &properties.Size{
										Width: &properties.Size_Dimension{
											Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
										},
									},
									Padding: &properties.PaddingProperty{
										Top:    12,
										Left:   dimensions.Padding(),
										Right:  dimensions.Padding(),
										Bottom: 12,
									},
								},
							},
						},
					},
				}),
			},
		}
	} else if data.ShouldShowAMBOnTrackMessage {
		return b.createAMBOnTrackSection(data)
	} else if data.ShouldShowNoAmbMessage {
		centeredText := &components.Component{
			Content: GetAnyWithoutError(&sections.VerticalListSection{
				Components: []*components.Component{
					{
						Content: GetAnyWithoutError(&ui.IconTextComponent{
							Texts: []*commontypes.Text{
								GetTextFromStringFontColourFontStyle(
									data.NoAmbMessage,
									colors.Subtle(),
									commontypes.FontStyle_SUBTITLE_M,
									commontypes.Text_ALIGNMENT_CENTER,
								),
							},
						}),
					},
				},
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Padding: &properties.PaddingProperty{
									Bottom: 40,
									Left:   dimensions.Padding(),
									Right:  dimensions.Padding(),
								},
							},
						},
					},
				},
				VisibleBehavior: &behaviors.LifecycleBehavior{
					Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
					AnalyticsEvent: &analytics.AnalyticsEvent{
						EventName: events.EventAMBSectionVisibleOnScreen,
						Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{
							Component:    "progressSection",
							SubComponent: "tierMessage",
						}),
					},
				},
			}),
		}
		componentsList = []*components.Component{centeredText}
	}

	return &components.Component{
		Content: GetAnyWithoutError(&sections.VerticalListSection{
			Components: componentsList,
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							Padding: &properties.PaddingProperty{
								Top: 48,
							},
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: colors.White(),
								},
							},
							Corner: &properties.CornerProperty{
								TopLeftCornerRadius:  dimensions.CornerRadius(),
								TopRightCornerRadius: dimensions.CornerRadius(),
								BottomLeftCorner:     dimensions.CornerRadius(),
								BottomRightCorner:    dimensions.CornerRadius(),
							},
							Margin: &properties.PaddingProperty{
								Top:    20,
								Bottom: dimensions.Margin(),
								Left:   dimensions.Padding(),
								Right:  dimensions.Padding(),
							},
						},
					},
				},
			},
		}),
	}
}

// createAddMoneyButton creates a button component for adding money
func (b *AMBScreenBuilderImpl) createAddMoneyButton(text, bgColor, textColor string, verticalPadding, horizontalPadding int32, dl *deeplink.Deeplink, data *models.AMBScreenData, sheet deeplink.Screen, sheetType *models.AMBBottomSheetType) *components.Component {
	var subComponent string
	if sheetType != nil {
		switch *sheetType {
		case models.AMB_SUMMARY:
			subComponent = "AMB_SUMMARY"
		case models.CURRENT_AMB:
			subComponent = "CURRENT_AMB"
		case models.TARGET_AMB:
			subComponent = "TARGET_AMB"
		case models.BALANCE_REQUIRED:
			subComponent = "BALANCE_REQUIRED"
		}
	}

	return &components.Component{
		Content: GetAnyWithoutError(&sections.HorizontalListSection{
			Components: []*components.Component{
				{
					Content: GetAnyWithoutError(&ui.IconTextComponent{
						Texts: []*commontypes.Text{
							GetTextFromStringFontColourFontStyle(text, textColor, commontypes.FontStyle_BUTTON_M, commontypes.Text_ALIGNMENT_LEFT),
						},
					}),
				},
			},
			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: bgColor,
								},
							},
							Corner: &properties.CornerProperty{
								BottomLeftCorner:     40,
								BottomRightCorner:    40,
								TopLeftCornerRadius:  40,
								TopRightCornerRadius: 40,
							},
							Size: &properties.Size{
								Width: &properties.Size_Dimension{
									Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
								},
							},
							Padding: &properties.PaddingProperty{Top: 12, Bottom: 12},
						},
					},
				},
			},
			InteractionBehaviors: []*behaviors.InteractionBehavior{
				{
					Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
						OnClickBehavior: &behaviors.OnClickBehavior{
							Action: GetAnyWithoutError(dl),
						},
					},
					AnalyticsEvent: &analytics.AnalyticsEvent{
						EventName: events.EventAMBVisibilityScreenActioned,
						Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{
							Component:    "addMoneyButton",
							SubComponent: subComponent,
							ActionType:   "click",
							ScreenName:   sheet.String(),
						}),
					},
				},
			},
		}),
	}
}

// createAMBHistorySection builds the AMB history section
func (b *AMBScreenBuilderImpl) createAMBHistorySection(data *models.AMBScreenData) *components.Component {
	// Get config values
	colors := b.config.Tiering().AMBScreen().Colors()
	dimensions := b.config.Tiering().AMBScreen().Dimensions()
	textContent := b.config.Tiering().AMBScreen().TextContent()

	// Create the history title directly as a component with left alignment and book icon
	historyTitle := &components.Component{
		Content: GetAnyWithoutError(&ui.IconTextComponent{
			Texts: []*commontypes.Text{
				GetTextFromStringFontColourFontStyle(
					textContent.YourAMBHistoryText(),
					colors.Secondary(),
					commontypes.FontStyle_BUTTON_M,
					commontypes.Text_ALIGNMENT_LEFT),
			},
			LeftVisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: b.config.Tiering().AMBScreen().Images().Book(),
						},
						Properties: &commontypes.VisualElementProperties{
							Height: 20,
							Width:  20,
						},
					},
				},
			},
		}),
	}

	// Create the history table using HTML
	// Generate history table HTML dynamically from the data
	tableHTML := "<table style=\"border-collapse: separate; border-spacing: 0; width: 100%; font-family: Arial, sans-serif; font-size: 10px; border: 1px solid #ddd; border-radius: 16px; overflow: hidden; text-align: left;\"><thead><tr style=\"background-color: #F8FAFA; color: #929599;\"><th style=\"padding: 8px; border-right: 1px solid #eee; color: #929599; border-top-left-radius: 16px;\">Dates</th><th style=\"padding: 8px; border-right: 1px solid #eee; color: #929599; width: 25%;\">Plan</th><th style=\"padding: 8px; color: #929599; border-top-right-radius: 16px;\">AMB Maintained</th></tr></thead><tbody>"

	for i, entry := range data.HistoryEntries {
		// Add border-bottom-left-radius and border-bottom-right-radius for the last row
		bottomLeftRadius := ""
		bottomRightRadius := ""
		if i == len(data.HistoryEntries)-1 {
			bottomLeftRadius = "border-bottom-left-radius: 16px;"
			bottomRightRadius = "border-bottom-right-radius: 16px;"
		}

		// Add height for middle rows
		height := ""
		if i > 0 && i < len(data.HistoryEntries)-1 {
			height = "height: 40px;"
		}

		// Use the color from AMBColor field, or default to #D65779 if not set
		ambColor := entry.AMBColor
		if ambColor == "" {
			ambColor = "#D65779"
		}

		row := fmt.Sprintf("<tr style=\"background-color: #fff; %s\">", height) +
			fmt.Sprintf("<td style=\"padding: 8px; border-top: 1px solid #eee; font-weight: bold; color: #313234; %s\">%s</td>", bottomLeftRadius, entry.DateRange) +
			fmt.Sprintf("<td style=\"padding: 8px; border-top: 1px solid #eee; font-weight: bold; color:  #313234; width: 25%%;\">%s</td>", entry.Tier) +
			fmt.Sprintf("<td style=\"padding: 8px; border-top: 1px solid #eee; color: %s; font-weight: bold; %s\">%s</td></tr>", ambColor, bottomRightRadius, entry.AMBMaintained)

		tableHTML += row
	}

	tableHTML += "</tbody></table>"

	historyTable := &components.Component{
		Content: GetAnyWithoutError(
			commontypes.GetHtmlText(tableHTML),
		),
	}

	// Create a wrapper for the title with margin
	titleWrapper := &sections.VerticalListSection{
		Components: []*components.Component{
			historyTitle,
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Margin: &properties.PaddingProperty{
							Top:    16,
							Bottom: 16,
						},
					},
				},
			},
		},
		HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
	}

	// Create a disclaimer text component
	disclaimerText := &components.Component{
		Content: GetAnyWithoutError(&ui.IconTextComponent{
			Texts: []*commontypes.Text{
				GetTextFromStringFontColourFontStyle(
					data.DisclaimerText,
					colors.Subtle(),
					commontypes.FontStyle_SUBTITLE_XS,
					commontypes.Text_ALIGNMENT_LEFT),
			},
		}),
	}

	// Create a wrapper for the disclaimer with margin
	disclaimerWrapper := &sections.VerticalListSection{
		Components: []*components.Component{
			disclaimerText,
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Margin: &properties.PaddingProperty{
							Top: 16,
						},
					},
				},
			},
		},
		HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
	}

	// Create the inner content with the title wrapper, table, and disclaimer
	innerContent := &sections.VerticalListSection{
		Components: []*components.Component{
			// Title with margin
			{Content: GetAnyWithoutError(titleWrapper)},
			// Table
			historyTable,
			lo.Ternary(!data.ShouldShowNoAmbMessage, &components.Component{Content: GetAnyWithoutError(disclaimerWrapper)}, nil),
		},
		HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Padding: &properties.PaddingProperty{
							Top:    16,
							Bottom: 16,
							Left:   16,
							Right:  16,
						},
						BgColor: widget.GetLinearGradientBackgroundColour(180, []*widget.ColorStop{
							{
								Color:          "#F0FAF8",
								StopPercentage: 0,
							},
							{
								Color:          "#FBFEFD",
								StopPercentage: 100,
							},
						}),
						Corner: &properties.CornerProperty{
							TopLeftCornerRadius:  dimensions.CornerRadius(),
							TopRightCornerRadius: dimensions.CornerRadius(),
							BottomLeftCorner:     dimensions.CornerRadius(),
							BottomRightCorner:    dimensions.CornerRadius(),
						},
					},
				},
			},
		},
		VisibleBehavior: &behaviors.LifecycleBehavior{
			Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
			AnalyticsEvent: &analytics.AnalyticsEvent{
				EventName: events.EventAMBSectionVisibleOnScreen,
				Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{
					Component: "ambHistorySection",
				}),
			},
		},
	}

	// Return the history section wrapped in a white container
	return &components.Component{
		Content: GetAnyWithoutError(&sections.VerticalListSection{
			Components: []*components.Component{
				{
					Content: GetAnyWithoutError(innerContent),
				},
			},
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							BgColor: widget.GetBlockBackgroundColour(colors.White()),
							Padding: &properties.PaddingProperty{
								Top:    dimensions.Padding(),
								Bottom: dimensions.Padding(),
								Left:   dimensions.Padding(),
								Right:  dimensions.Padding(),
							},
							Size: &properties.Size{
								Width: &properties.Size_Dimension{
									Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
								},
							},
						},
					},
				},
			},
		}),
	}
}

// createLearningCenterSection builds the learning center section
func (b *AMBScreenBuilderImpl) createLearningCenterSection(data *models.AMBScreenData) *components.Component {
	// Get config values
	colors := b.config.Tiering().AMBScreen().Colors()
	dimensions := b.config.Tiering().AMBScreen().Dimensions()
	textContent := b.config.Tiering().AMBScreen().TextContent()

	// Get both titles and links
	learningItems := b.config.Tiering().AMBScreen().TextContent().LearningItems().Titles().ToStringArray()
	learningLinks := b.config.Tiering().AMBScreen().TextContent().LearningItems().StorifiLinks().ToStringArray()
	learningImages := b.config.Tiering().AMBScreen().TextContent().LearningItems().Images().ToStringArray()

	// Create title with left margin applied properly through a vertical list section
	learningCenterTitle := &components.Component{
		Content: GetAnyWithoutError(&sections.VerticalListSection{
			Components: []*components.Component{
				CreateText(textContent.LearningCenterText(), colors.Text(), commontypes.FontStyle_DISPLAY_M),
			},
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							Padding: &properties.PaddingProperty{
								Left:   6,
								Top:    6,
								Bottom: 6,
							},
						},
					},
				},
			},
		}),
	}

	// Helper function to create a learning item with clickable behavior
	createLearningItem := func(index int) *components.Component {
		return &components.Component{
			Content: GetAnyWithoutError(&sections.VerticalListSection{
				Components: []*components.Component{
					{
						Content: GetAnyWithoutError(&sections.HorizontalListSection{
							Components: []*components.Component{
								CreateText(learningItems[index], "#2D5E6E", commontypes.FontStyle_HEADLINE_S),
								{Content: GetAnyWithoutError(&components.Spacer{
									Weight: &components.Spacer_Weight{
										Value: 100,
									},
								})},
							},
						}),
					},
					{Content: GetAnyWithoutError(&components.Spacer{
						Weight: &components.Spacer_Weight{
							Value: 100,
						},
					})},
					{
						Content: GetAnyWithoutError(&sections.HorizontalListSection{
							Components: []*components.Component{
								{Content: GetAnyWithoutError(&components.Spacer{
									Weight: &components.Spacer_Weight{
										Value: 100,
									},
								})},
								CreateImage(learningImages[index], 64, 64),
							},
						}),
					},
				},
				// Add interaction behavior to open the provided link
				InteractionBehaviors: []*behaviors.InteractionBehavior{
					{
						Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
							OnClickBehavior: &behaviors.OnClickBehavior{
								Action: GetAnyWithoutError(&deeplink.Deeplink{
									Screen: deeplink.Screen_STORY_SCREEN,
									ScreenOptions: &deeplink.Deeplink_StoryScreenOptions{
										StoryScreenOptions: &deeplink.StoryScreenOptions{
											StoryUrl: learningLinks[index],
										},
									},
								}),
							},
						},
						AnalyticsEvent: &analytics.AnalyticsEvent{
							EventName: events.EventAMBVisibilityScreenActioned,
							Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{
								Component:    "learningCenter",
								SubComponent: fmt.Sprintf("learningItem_%d", index),
								ActionType:   "click",
								ScreenName:   deeplink.Screen_STORY_SCREEN.String(),
							}),
						},
					},
				},
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Padding: &properties.PaddingProperty{
									Top:    dimensions.Padding(),
									Left:   dimensions.Padding(),
									Bottom: dimensions.Padding(),
									Right:  dimensions.Padding(),
								},
								Corner: &properties.CornerProperty{
									TopLeftCornerRadius:  12,
									TopRightCornerRadius: 12,
									BottomLeftCorner:     12,
									BottomRightCorner:    12,
								},
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type:   properties.Size_Dimension_DIMENSION_TYPE_WEIGHT,
										Weight: &properties.Weight{Value: 50},
									},
									Height: &properties.Size_Dimension{
										Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
										ExactValue: 140,
									},
								},
								Margin: &properties.PaddingProperty{
									Left:  6,
									Right: 6,
								},
								BgColor: widget.GetLinearGradientBackgroundColour(180, []*widget.ColorStop{
									{
										Color:          "#4DE4F1F5",
										StopPercentage: 0,
									},
									{
										Color:          "#4DA6D9E9",
										StopPercentage: 100,
									},
								}),
								Border: &properties.BorderProperty{
									BorderThickness: 1,
									BorderColor:     "#BCDCE7",
									CornerRadius:    12,
								},
							},
						},
					},
				},
			}),
		}
	}

	// Create learning items dynamically in rows of two using a for loop
	// with both title and link information
	var learningRows []*components.Component
	for i := 0; i < len(learningItems); i += 2 {
		// Create first item with its link
		var rowComponents []*components.Component
		if i < len(learningItems) && i < len(learningLinks) {
			rowComponents = append(rowComponents, createLearningItem(i))
		}

		// Create second item if available
		if i+1 < len(learningItems) && i+1 < len(learningLinks) {
			rowComponents = append(rowComponents, createLearningItem(i+1))
		}

		// Create the row if we have components
		if len(rowComponents) > 0 {
			row := &components.Component{
				Content: GetAnyWithoutError(&sections.HorizontalListSection{
					Components: rowComponents,
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Margin: &properties.PaddingProperty{
										Top:    8,
										Bottom: 8,
									},
								},
							},
						},
					},
					HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
				}),
			}
			learningRows = append(learningRows, row)
		}
	}

	// Create the inner learning center section with the existing styling
	innerLearningCenterSection := &sections.VerticalListSection{
		Components:          append([]*components.Component{learningCenterTitle}, learningRows...),
		HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Padding: &properties.PaddingProperty{
							Left:   dimensions.Padding(),
							Right:  dimensions.Padding(),
							Top:    24,
							Bottom: 24,
						},
					},
				},
			},
		},
	}

	// Return the learning center section wrapped in a white container that fills the screen width
	return &components.Component{
		Content: GetAnyWithoutError(&sections.VerticalListSection{
			Components: []*components.Component{
				{
					Content: GetAnyWithoutError(innerLearningCenterSection),
				},
			},
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							BgColor: widget.GetBlockBackgroundColour(colors.White()),
							Size: &properties.Size{
								Width: &properties.Size_Dimension{
									Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
								},
							},
							Margin: &properties.PaddingProperty{
								Top: 4,
							},
						},
					},
				},
			},
			VisibleBehavior: &behaviors.LifecycleBehavior{
				Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
				AnalyticsEvent: &analytics.AnalyticsEvent{
					EventName: events.EventAMBSectionVisibleOnScreen,
					Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{
						Component: "learningSection",
					}),
				},
			},
		}),
	}
}

// formatCurrency formats a Money object as a currency string
func formatCurrency(amount *moneyPb.Money) string {
	// Handle nil case
	if amount == nil {
		// Return zero amount formatted
		zeroAmount := money.ZeroINR().GetPb()
		return money.ToDisplayStringInIndianFormat(zeroAmount, 0, true)
	}

	// Use money package to format the currency
	return money.ToDisplayStringInIndianFormat(amount, 0, true)
}

// createAMBOnTrackSection builds an AMB on track section with checkmark, text and lottie overlay
func (b *AMBScreenBuilderImpl) createAMBOnTrackSection(data *models.AMBScreenData) *components.Component {
	// Get config values
	dimensions := b.config.Tiering().AMBScreen().Dimensions()

	// Create checkmark icon using data from the model
	checkmarkIcon := &commontypes.VisualElement{
		Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{
					Url: data.AMBOnTrackIconURL,
				},
				Properties: &commontypes.VisualElementProperties{
					Height: 48,
					Width:  48,
				},
			},
		},
	}

	// --- Pro Tip Logic ---
	proTipParts := []string{b.config.Tiering().AMBScreen().TextContent().AMBOnTrackProTipText()}
	var proTipDeeplink *deeplink.Deeplink

	// Use computed flag from data collector for special pro tip display
	if data.ShouldShowSpecialProTip {
		// Get the special pro tip component for this tier
		if proTipComponent := b.config.Tiering().AMBScreen().TextContent().AMBOnTrackProTipMap().Get(data.CurrentTier.String()); proTipComponent != nil {
			if proTipComponent.HtmlText() != "" {
				proTipParts = []string{proTipComponent.HtmlText()}
			}
			// Generate deeplink to next higher tier if screen is specified
			if external.Tier_value[proTipComponent.NextHigherTier()] > 0 {
				proTipDeeplink = b.getTierPlansDeeplink(external.Tier(external.Tier_value[proTipComponent.NextHigherTier()]))
			}
		}
	}

	var proTipTexts []*commontypes.Text
	for _, part := range proTipParts {
		proTipTexts = append(proTipTexts, &commontypes.Text{
			DisplayValue: &commontypes.Text_Html{
				Html: part,
			},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
			FontColor: "#333333",
			Alignment: commontypes.Text_ALIGNMENT_CENTER,
		})
	}

	// Create the background content layer
	backgroundContent := &components.Component{
		Content: GetAnyWithoutError(&sections.VerticalListSection{
			Components: []*components.Component{
				// initial spacing
				{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_S})},
				// Main message section with better layout
				{
					Content: GetAnyWithoutError(&sections.HorizontalListSection{
						Components: []*components.Component{
							{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_L})},
							// Checkmark icon
							{
								Content: GetAnyWithoutError(checkmarkIcon),
							},
							// Spacer for better spacing
							{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_XS})},
							// Main text with flex to center
							{
								Content: GetAnyWithoutError(&ui.IconTextComponent{
									Texts: []*commontypes.Text{
										GetTextFromStringFontColourFontStyle(data.AMBOnTrackMainText, data.AMBOnTrackTextColor, commontypes.FontStyle_SUBTITLE_M, commontypes.Text_ALIGNMENT_LEFT),
									},
								}),
							},
							// Right spacer for centering
							{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_L})},
						},
						HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
					}),
				},
				// Spacer between main message and pro tip
				{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_M})},
				// Pro tip text - centered
				{
					Content: GetAnyWithoutError(&sections.HorizontalListSection{
						Components: []*components.Component{
							{
								Content: GetAnyWithoutError(&ui.IconTextComponent{
									Texts: proTipTexts,
								}),
								InteractionBehaviors: lo.Ternary(proTipDeeplink != nil, []*behaviors.InteractionBehavior{
									{
										Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
											OnClickBehavior: &behaviors.OnClickBehavior{
												Action: GetAnyWithoutError(proTipDeeplink),
											},
										},
										AnalyticsEvent: &analytics.AnalyticsEvent{
											EventName: events.EventAMBVisibilityScreenActioned,
											Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{
												Component:    "proTip",
												SubComponent: "nextTierPromotion",
												ActionType:   "click",
												ScreenName:   deeplink.Screen_TIER_ALL_PLANS_SCREEN_V2.String(),
											}),
										},
									},
								}, nil),
							},
						},
						HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
					}),
				},
			},
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							Padding: &properties.PaddingProperty{
								Top:    24,
								Left:   24,
								Right:  24,
								Bottom: 24,
							},
							// Brownish gradient background similar to the image
							BgColor: data.ProgressBackgroundGradient,
							Corner: &properties.CornerProperty{
								TopLeftCornerRadius:  dimensions.CornerRadius(),
								TopRightCornerRadius: dimensions.CornerRadius(),
								BottomLeftCorner:     dimensions.CornerRadius(),
								BottomRightCorner:    dimensions.CornerRadius(),
							},
							Margin: &properties.PaddingProperty{
								Top:    20,
								Left:   dimensions.Padding(),
								Right:  dimensions.Padding(),
								Bottom: dimensions.Padding(),
							},
						},
					},
				},
			},
			VisibleBehavior: &behaviors.LifecycleBehavior{
				Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
				AnalyticsEvent: &analytics.AnalyticsEvent{
					EventName: events.EventAMBSectionVisibleOnScreen,
					Properties: data.GetAnalyticsProperties(models.AnalyticsComponentContext{
						Component: "ambOnTrackSection",
					}),
				},
			},
		}),
	}

	// Create lottie overlay layer if URL is provided
	var lottieOverlay *components.Component
	if data.ShouldShowAMBOnTrackLottie {
		lottieOverlay = &components.Component{
			Content: GetAnyWithoutError(&sections.HorizontalListSection{
				Components: []*components.Component{
					{
						Content: GetAnyWithoutError(&commontypes.VisualElement{
							Asset: &commontypes.VisualElement_Lottie_{
								Lottie: &commontypes.VisualElement_Lottie{
									Source: &commontypes.VisualElement_Lottie_Url{
										Url: data.AMBOnTrackLottieURL,
									},
									Properties: &commontypes.VisualElementProperties{
										Width:  300,
										Height: 300,
									},
									RepeatCount: -1,
								},
							},
						}),
					},
				},
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
									Height: &properties.Size_Dimension{
										Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
										ExactValue: 150,
									},
								},
							},
						},
					},
				},
			}),
		}
	}

	// Create components list for depth-wise layering
	var depthComponents []*components.Component
	depthComponents = append(depthComponents, backgroundContent)
	if lottieOverlay != nil {
		depthComponents = append(depthComponents, lottieOverlay)
	}

	return &components.Component{
		Content: GetAnyWithoutError(&sections.DepthWiseListSection{
			Components: depthComponents,
			Alignment:  sections.DepthWiseListSection_CENTER_CENTER,
		}),
	}
}

// getTierPlansDeeplink generates a deeplink to the next higher tier
func (b *AMBScreenBuilderImpl) getTierPlansDeeplink(tier external.Tier) *deeplink.Deeplink {
	metaData := &tieringPb.TieringScreenMetaData{
		Metadata: &tieringPb.TieringScreenMetaData_PlansV2Metadata{
			PlansV2Metadata: &tieringPb.PlansV2Metadata{
				TierToFocus: tier.String(),
			},
		},
	}
	metaDataMarshalled, metaDataMarshalErr := protojson.Marshal(metaData)
	if metaDataMarshalErr != nil {
		return nil
	}

	dl := &deeplink.Deeplink{
		Screen: deeplink.Screen_TIER_ALL_PLANS_SCREEN_V2,
		ScreenOptionsV2: GetAnyWithoutError(
			&tiering.AllPlansV2ScreenOptions{
				MetaData: string(metaDataMarshalled),
			},
		),
	}

	return dl
}
