package pay

import (
	"context"
	"crypto/rand"
	"errors"
	"fmt"
	"math"
	"math/big"
	"strconv"
	"strings"
	"time"

	fePkgUpi "github.com/epifi/gamma/frontend/pkg/upi"

	"github.com/samber/lo"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsPinotPb "github.com/epifi/gamma/api/rewards/pinot"
	"github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/pkg/feature/release"
	rewardsPkg "github.com/epifi/gamma/pkg/rewards"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/logger"

	dePb "github.com/epifi/gamma/api/dynamic_elements"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	feUpiOnbEnumsPb "github.com/epifi/gamma/api/frontend/upi/onboarding/enums"
	tieringPb "github.com/epifi/gamma/api/tiering"
	typesPb "github.com/epifi/gamma/api/typesv2"
	upiScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/upi"
	"github.com/epifi/gamma/api/typesv2/ui"
	payConfig "github.com/epifi/gamma/pay/config/server"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

const (
	// RewardsPeriodCurrentMonth represents the current month
	RewardsPeriodCurrentMonth = "CURRENT_MONTH"
	// RewardsPeriodLastMonth represents the previous month
	RewardsPeriodLastMonth = "LAST_MONTH"
	// RewardsPeriodCurrentWeek represents the current week
	RewardsPeriodCurrentWeek = "CURRENT_WEEK"

	TierIconUrlPlaceholder          = "tier_icon_url"
	RewardsPercentagePlaceholder    = "rewards_percentage"
	FiCoinsMonthlyEarnedPlaceholder = "fi_coins_monthly_earned"
	moneyPlantColorIcon             = "https://epifi-icons.pointz.in/tiering/earned-benefits/money-plant-color.png"
	moneyPlantBlackAndWhiteIcon     = "https://epifi-icons.pointz.in/tiering/earned-benefits/money-plant-black-and-white.png"
	fiCoinsIcon                     = "https://epifi-icons.pointz.in/tiering/earned-benefits/golden-fi-coins.png"
)

// Variables are now config-driven, removed hardcoded maps here

func (s *Service) FetchDynamicElements(ctx context.Context, req *dePb.FetchDynamicElementsRequest) (*dePb.FetchDynamicElementsResponse, error) {
	// Use switch statement for request type determination as suggested in PR comments
	switch {
	case req.GetClientContext() != nil && lo.Contains([]deeplinkPb.Screen{deeplinkPb.Screen_PAY_ML_KIT_QR_SCREEN, deeplinkPb.Screen_PAY_QR_SCREEN}, req.GetClientContext().GetScreenName()):
		return s.getQrScanDynamicElementsV2(ctx, req.GetActorId())
	case req.GetClientContext() != nil && lo.Contains([]deeplinkPb.Screen{deeplinkPb.Screen_POST_PAYMENT_SCREEN}, req.GetClientContext().GetScreenName()):
		return s.fetchPostPaymentDynamicElements()
	case req.GetClientContext() != nil && lo.Contains([]deeplinkPb.Screen{deeplinkPb.Screen_PAY_LANDING_SCREEN}, req.GetClientContext().GetScreenName()):
		return s.getPayLandingScreenElements(ctx, req.GetActorId())
	default:
		return nil, nil
	}
}

func (s *Service) DynamicElementCallback(_ context.Context, _ *dePb.DynamicElementCallbackRequest) (*dePb.DynamicElementCallbackResponse, error) {
	return &dePb.DynamicElementCallbackResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) fetchPostPaymentDynamicElements() (*dePb.FetchDynamicElementsResponse, error) {
	var elementList []*dePb.DynamicElement
	for _, banner := range s.conf.PostPaymentBanners {
		deeplink, err := getDeeplinkForBanner(banner)
		if err != nil {
			return nil, fmt.Errorf("error while generating deeplink for the banner : %w", err)
		}

		dynamicElement := &dePb.DynamicElement{
			OwnerService:  typesPb.ServiceName_PAY_SERVICE,
			UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
			StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2,
			Content: &dePb.ElementContent{
				Content: &dePb.ElementContent_BannerV2{
					BannerV2: &dePb.BannerElementContentV2{
						Title: &commontypes.Text{
							FontColor: banner.Title.Color,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: banner.Title.Text,
							},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle(commontypes.FontStyle_value[banner.Title.Style]),
							},
						},
						// Note: iOS requires body to be present whereas Android only uses Title
						Body: &commontypes.Text{
							FontColor: banner.Title.Color,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: banner.Title.Text,
							},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle(commontypes.FontStyle_value[banner.Title.Style]),
							},
						},
						Image: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  banner.Image.Url,
							Width:     banner.Image.Width,
							Height:    banner.Image.Height,
						},
						// Note: iOS requires VisualElement to be present whereas Android only uses Image
						VisualElement: &commontypes.VisualElement{
							Asset: &commontypes.VisualElement_Image_{
								Image: &commontypes.VisualElement_Image{
									ImageType: commontypes.ImageType_PNG,
									Source: &commontypes.VisualElement_Image_Url{
										Url: banner.Image.Url,
									},
									Properties: &commontypes.VisualElementProperties{
										Height: banner.Image.Height,
										Width:  banner.Image.Width,
									},
								},
							},
						},
						BackgroundColor: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_BlockColour{
								BlockColour: banner.BgColor,
							},
						},
						Deeplink: deeplink,
					},
				},
			},
		}
		elementList = append(elementList, dynamicElement)
	}
	return &dePb.FetchDynamicElementsResponse{
		Status:       rpc.StatusOk(),
		ElementsList: elementList,
	}, nil
}

func (s *Service) getPayLandingScreenElements(ctx context.Context, actorId string) (*dePb.FetchDynamicElementsResponse, error) {
	isDynamicElementsEnabled, dynamicEleErr := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_PAY_LANDING_SCREEN_BANNER_DYNAMIC_ELEMENTS).WithActorId(actorId))
	if dynamicEleErr != nil {
		logger.Error(ctx, "error evaluating ruPay Credit Card linking banner of pay landing screen", zap.Error(dynamicEleErr), zap.String(logger.ACTOR_ID_V2, actorId))
	}
	if !isDynamicElementsEnabled {
		logger.Debug(ctx, "Feature Flag for Showing Pay Landing screen banner via dynamic elements is not yet enabled", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, nil
	}
	var elementList []*dePb.DynamicElement
	for _, banner := range s.conf.PayLandingScreenBanners {
		if !banner.IsEnabled || !s.shouldShowBanner(ctx, banner.Deeplink, actorId) {
			continue
		}
		deeplink, err := getDeeplinkForBanner(banner)
		if err != nil {
			return nil, fmt.Errorf("error while generating deeplink for the banner : %w", err)
		}

		dynamicElement := &dePb.DynamicElement{
			OwnerService:  typesPb.ServiceName_PAY_SERVICE,
			UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
			StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V3,
			Content: &dePb.ElementContent{
				Content: &dePb.ElementContent_BannerV3{
					BannerV3: &dePb.BannerElementContentV3{
						Title: &commontypes.Text{
							FontColor: banner.Title.Color,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: banner.Title.Text,
							},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle(commontypes.FontStyle_value[banner.Title.Style]),
							},
						},
						Body: &ui.IconTextComponent{
							Texts: []*commontypes.Text{
								commontypes.GetTextFromStringFontColourFontStyle(
									banner.Body.Text,
									banner.Body.Color,
									(commontypes.FontStyle)(commontypes.FontStyle_value[banner.Body.Style])),
							},
						},
						RightVisualElement: &commontypes.VisualElement{
							Asset: &commontypes.VisualElement_Image_{
								Image: &commontypes.VisualElement_Image{
									ImageType: commontypes.ImageType_PNG,
									Source: &commontypes.VisualElement_Image_Url{
										Url: banner.Image.Url,
									},
									Properties: &commontypes.VisualElementProperties{
										Height: banner.Image.Height,
										Width:  banner.Image.Width,
									},
								},
							},
						},
						BgColour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_LinearGradient{
								LinearGradient: &widget.LinearGradient{
									Degree: 180,
									LinearColorStops: []*widget.ColorStop{
										{
											Color:          "#00DCF3EE",
											StopPercentage: -57,
										},
										{
											Color:          "#3FDCF3EE",
											StopPercentage: 100,
										},
									},
								},
							},
						},
						BorderColour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{
								BlockColour: "#B2DCF3EE",
							},
						},
						Deeplink: deeplink,
					},
				},
			},
		}
		elementList = append(elementList, dynamicElement)
	}
	return &dePb.FetchDynamicElementsResponse{
		Status:       rpc.StatusOk(),
		ElementsList: elementList,
	}, nil
}

// bannerInfo contains all the data fetched for the elements
type bannerInfo struct {
	tier                 *tieringPb.GetTierAtTimeResponse
	rewardsAggr          *rewardsPinotPb.GetRewardsAggregatesResponse
	projectedRewardsAggr *rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse
}

type qrScreenElement interface {
	GetElementType() dePb.ElementStructureType
}

// getQrScanDynamicElementsV2 implements the new approach with QrScreenElements
func (s *Service) getQrScanDynamicElementsV2(ctx context.Context, actorId string) (*dePb.FetchDynamicElementsResponse, error) {
	// Feature flag check for QR Scan Enhancements
	constraints := release.NewCommonConstraintData(typesPb.Feature_FEATURE_QR_SCAN_ENHANCEMENTS).WithActorId(actorId)
	isAllowed, err := s.releaseEvaluator.Evaluate(ctx, constraints)
	if err != nil {
		logger.Error(ctx, "error evaluating if qr banners is enabled", zap.Error(err))
		return &dePb.FetchDynamicElementsResponse{Status: rpc.StatusInternal()}, nil
	}

	if !isAllowed {
		// Return empty list if feature is not allowed
		return &dePb.FetchDynamicElementsResponse{
			Status:       rpc.StatusOk(),
			ElementsList: []*dePb.DynamicElement{},
		}, nil
	}

	var (
		qrScreenElements []qrScreenElement
	)
	// Check if there are any QR screen elements configured
	if s.conf.QrScreenElements == nil {
		return &dePb.FetchDynamicElementsResponse{
			Status:       rpc.StatusOk(),
			ElementsList: []*dePb.DynamicElement{},
		}, nil
	}

	for _, element := range s.conf.QrScreenElements.Banners {
		qrScreenElements = append(qrScreenElements, element)
	}

	for _, element := range s.conf.QrScreenElements.ProgressBars {
		qrScreenElements = append(qrScreenElements, element)
	}

	// Pick one element at random using crypto/rand
	n, err := rand.Int(rand.Reader, big.NewInt(int64(len(qrScreenElements))))
	if err != nil {
		logger.Error(ctx, "Error generating random number", zap.Error(err))
		return &dePb.FetchDynamicElementsResponse{Status: rpc.StatusInternal()}, nil
	}
	elementIndex := n.Int64()
	selectedElement := qrScreenElements[elementIndex]

	// Fetch data required for the selected element
	elementData, err := s.fetchElementData(ctx, actorId, selectedElement)
	if err != nil {
		logger.Error(ctx, "Error fetching element data", zap.Error(err), zap.String("actorId", actorId))
		return &dePb.FetchDynamicElementsResponse{Status: rpc.StatusFromError(err)}, nil
	}

	// Create the dynamic element based on the element type
	switch element := selectedElement.(type) {
	case *payConfig.Banner:
		return s.createBannerElement(ctx, element, elementData)
	case *payConfig.ProgressBar:
		return s.createProgressBarElement(element, elementData)
	default:
		// Fallback to empty response for unknown element types
		return &dePb.FetchDynamicElementsResponse{
			Status:       rpc.StatusOk(),
			ElementsList: []*dePb.DynamicElement{},
		}, nil
	}
}

// fetchElementData fetches all required data for a QR screen element based on its configuration
func (s *Service) fetchElementData(ctx context.Context, actorId string, element qrScreenElement) (*bannerInfo, error) {
	data := &bannerInfo{}

	// Determine data requirements based on element type
	var requiresTieringData bool
	var rewardsConfig interface{}

	switch e := element.(type) {
	case *payConfig.Banner:
		if e.DataRequirements != nil {
			requiresTieringData = e.DataRequirements.RequiresTieringData
			rewardsConfig = e.DataRequirements.RewardsConfig
		}
	case *payConfig.ProgressBar:
		if e.DataRequirements != nil {
			requiresTieringData = e.DataRequirements.RequiresTieringData
			rewardsConfig = e.DataRequirements.RewardsConfig
		}
	}

	// First fetch tiering data if required - this needs to be done first
	// since rewards data might depend on the tier information
	if requiresTieringData {
		fetchCtx, cancel := context.WithTimeout(ctx, 1*time.Second)
		defer cancel()

		res, err := s.tieringClient.GetTierAtTime(fetchCtx, &tieringPb.GetTierAtTimeRequest{
			ActorId:       actorId,
			TierTimestamp: timestamp.Now(), // Current time
		})
		if te := epifigrpc.RPCError(res, err); te != nil {
			return nil, te
		}

		data.tier = res

		// not a valid tier for banners
		if data.tier.GetTierInfo().GetTier() <= external.Tier_TIER_FI_BASIC {
			return nil, errors.New(fmt.Sprintf("tier is not eligible for banners: %s", data.tier.GetTierInfo().GetTier().String()))
		}
	}

	// Now fetch rewards data if required - this happens after tier data is fetched (if required)
	if fetchRewardsData(rewardsConfig) {
		fetchCtx, cancel := context.WithTimeout(ctx, 1*time.Second)
		defer cancel()

		// Set time range based on configuration
		from, to := getTimeRangeForRewardsConfig(rewardsConfig)

		// Create request for rewards data
		rewardsRequest := &rewardsPinotPb.GetRewardsAggregatesRequest{
			ActorId:       actorId,
			FromCreatedAt: timestamp.New(from),
			ToCreatedAt:   timestamp.New(to),
			Filters: &rewardsPinotPb.Filters{
				RewardType:         rewardsPb.RewardType_FI_COINS,
				RewardOfferTypes:   append(rewardsPkg.BeTierToRewardOfferTypeMap[data.tier.GetTierInfo().GetTier()], rewardsPb.RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE),
				IncludeActionTypes: []rewardsPb.CollectedDataType{rewardsPb.CollectedDataType_ORDER, rewardsPb.CollectedDataType_TIERING_PERIODIC_REWARD_EVENT},
				AccountTier:        data.tier.GetTierInfo().GetTier(),
			},
		}

		res, err := s.rewardsAggregatesClient.GetRewardsAggregates(fetchCtx, rewardsRequest)
		if te := epifigrpc.RPCError(res, err); te != nil {
			return nil, te
		}

		data.rewardsAggr = res

		if len(data.rewardsAggr.GetRewardOptionAggregates()) == 0 {
			return nil, rpc.StatusAsError(rpc.StatusRecordNotFound())
		}
	}

	// Now fetch projected rewards data if required
	if fetchProjectedRewardsData(rewardsConfig) {
		fetchCtx, cancel := context.WithTimeout(ctx, 1*time.Second)
		defer cancel()

		// Set time range based on configuration
		from, to := getTimeRangeForRewardsConfig(rewardsConfig)

		// Set daily and monthly limits based on reward type and tier
		var dailyLimit, monthlyLimit int64
		tier := data.tier.GetTierInfo().GetTier()

		dailyLimit = int64(rewardsPkg.BeTierToFiCoinsDailyCapMap[tier])
		monthlyLimit = int64(rewardsPkg.BeTierToFiCoinsMonthlyCapMap[tier])

		// Create request for projected rewards data using the new RPC
		projectedRewardsRequest := &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesRequest{
			ActorId:    actorId,
			RewardType: rewardsPb.RewardType_FI_COINS,
			TimeRange: &rewardsPinotPb.TimeRangeFilter{
				Type: rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME,
				Range: &rewardsPinotPb.TimeRange{
					From: timestamp.New(from),
					To:   timestamp.New(to),
				},
			},
			ProjectionCustomQuery: &rewardsPinotPb.ProjectionCustomQuery{
				Type: rewardsPinotPb.ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE,
				Metadata: &rewardsPinotPb.ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata{
					TieringMonthlyProjectionsAggregateMetadata: &rewardsPinotPb.TieringMonthlyProjectionsAggregateMetadata{
						ActionType:   rewardsPb.CollectedDataType_ORDER,
						OfferTypes:   rewardsPkg.BeTierToRewardOfferTypeMap[tier],
						DailyLimit:   dailyLimit,
						MonthlyLimit: monthlyLimit,
					},
				},
			},
		}

		res, err := s.rewardsAggregatesClient.GetCustomUnActualizedRewardProjectionsAggregates(fetchCtx, projectedRewardsRequest)
		if te := epifigrpc.RPCError(res, err); te != nil {
			return nil, te
		}

		if res.GetRewardProjectionsAggregates().GetRewardUnits() == 0 {
			return nil, rpc.StatusAsError(rpc.StatusRecordNotFound())
		}

		data.projectedRewardsAggr = res
	}

	return data, nil
}

// Helper function to determine if rewards data should be fetched
func fetchRewardsData(rewardsConfig interface{}) bool {
	if rewardsConfig == nil {
		return false
	}

	switch config := rewardsConfig.(type) {
	case *payConfig.BannerRewardsConfig:
		return config != nil && config.RequiresRewardsData
	case *payConfig.RewardsConfig:
		return config != nil && config.RequiresRewardsData
	default:
		return false
	}
}

// Helper function to get the time range based on the rewards configuration
func getTimeRangeForRewardsConfig(rewardsConfig interface{}) (time.Time, time.Time) {
	var timePeriod string

	switch config := rewardsConfig.(type) {
	case *payConfig.BannerRewardsConfig:
		if config != nil {
			timePeriod = config.TimePeriod
		}
	case *payConfig.RewardsConfig:
		if config != nil {
			timePeriod = config.TimePeriod
		}
	}

	return getRewardsTimeRange(timePeriod)
}

// Helper function to determine if projected rewards data should be fetched
func fetchProjectedRewardsData(rewardsConfig interface{}) bool {
	if rewardsConfig == nil {
		return false
	}

	switch config := rewardsConfig.(type) {
	case *payConfig.BannerRewardsConfig:
		return config != nil && config.RequiresProjectedRewardsData
	case *payConfig.RewardsConfig:
		return config != nil && config.RequiresProjectedRewardsData
	default:
		return false
	}
}

func (s *Service) createBannerElement(ctx context.Context, banner *payConfig.Banner, data *bannerInfo) (*dePb.FetchDynamicElementsResponse, error) {
	// Generate deeplink for the selected banner
	deeplink, err := getDeeplinkForBanner(banner)
	if err != nil {
		logger.Error(ctx, "Error while generating deeplink for QR scan banner", zap.Error(err))
		return nil, fmt.Errorf("error while generating deeplink for the banner: %w", err)
	}

	placeholderMap := s.getPlaceholderMap(data)

	// Create the dynamic element
	dynamicElement := &dePb.DynamicElement{
		OwnerService:  typesPb.ServiceName_PAY_SERVICE,
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V3,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_BannerV3{
				BannerV3: &dePb.BannerElementContentV3{
					Title: &commontypes.Text{
						FontColor: banner.Title.Color,
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: replacePlaceholders(banner.Title.Text, placeholderMap),
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle(commontypes.FontStyle_value[banner.Title.Style]),
						},
					},
					// Set body as an IconTextComponent
					Body: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle(replacePlaceholders(banner.Body.Text, placeholderMap), banner.Body.Color, commontypes.FontStyle(commontypes.FontStyle_value[banner.Body.Style])),
						},
					},
					// Right visual element for the image
					LeftVisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								ImageType: commontypes.ImageType_PNG,
								Source: &commontypes.VisualElement_Image_Url{
									Url: replacePlaceholders(banner.Image.Url, placeholderMap),
								},
								Properties: &commontypes.VisualElementProperties{
									Height: banner.Image.Height,
									Width:  banner.Image.Width,
								},
							},
						},
					},
					BgColour: widget.GetBlockBackgroundColour(banner.BgColor),
					Deeplink: deeplink,
				},
			},
		},
	}

	return &dePb.FetchDynamicElementsResponse{
		Status:       rpc.StatusOk(),
		ElementsList: []*dePb.DynamicElement{dynamicElement},
	}, nil
}

func (s *Service) createProgressBarElement(progressBar *payConfig.ProgressBar, data *bannerInfo) (*dePb.FetchDynamicElementsResponse, error) {
	// Calculate progress percentage for the progress bar
	projectedValue := 0.0
	maxValue := float64(rewardsPkg.BeTierToFiCoinsMonthlyCapMap[data.tier.GetTierInfo().GetTier()])

	placeholderMap := s.getPlaceholderMap(data)

	// If projected rewards data is available and configured, update projected values
	if data.projectedRewardsAggr != nil &&
		progressBar.DataRequirements != nil &&
		progressBar.DataRequirements.RewardsConfig != nil &&
		progressBar.DataRequirements.RewardsConfig.RequiresProjectedRewardsData {

		// Get the projected value directly from the response
		projectedValue = float64(data.projectedRewardsAggr.GetRewardProjectionsAggregates().GetRewardUnits())
	}

	// Calculate percentage (capped at 100%)
	percentComplete := int32(math.Ceil((projectedValue / maxValue) * 100))
	if percentComplete > 100 {
		percentComplete = 100
	}

	// Create progress bar element with the correct fields from the proto definition
	dynamicElement := &dePb.DynamicElement{
		OwnerService:  typesPb.ServiceName_PAY_SERVICE,
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_PROGRESS_BAR_CARD,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_ProgressBarElement{
				ProgressBarElement: &dePb.ProgressBarCardContent{
					Title: &commontypes.Text{
						FontColor: progressBar.Title.Color,
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: replacePlaceholders(progressBar.Title.Text, placeholderMap),
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle(commontypes.FontStyle_value[progressBar.Title.Style]),
						},
						Alignment: commontypes.Text_ALIGNMENT_LEFT,
					},
					// Using the correct field name for subtitle content
					SubtitleContent: []*dePb.ProgressBarCardContent_SubtitleContent{
						{
							Subtitle: &ui.IconTextComponent{
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(fiCoinsIcon, 14, 14),
							},
						},
						{
							Subtitle: &ui.IconTextComponent{
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle(strconv.FormatInt(int64(projectedValue), 10), "#D5AB52", commontypes.FontStyle_HEADLINE_XS),
								},
							},
						},
						{
							Subtitle: &ui.IconTextComponent{
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle(" /", "#D5AB52", commontypes.FontStyle_HEADLINE_XS),
								},
							},
						},
						{
							Subtitle: &ui.IconTextComponent{
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(fiCoinsIcon, 14, 14),
							},
						},
						{
							Subtitle: &ui.IconTextComponent{
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle(strconv.FormatInt(int64(maxValue), 10), "#D5AB52", commontypes.FontStyle_HEADLINE_XS),
								},
							},
						},
					},
					// Using the correct field name for progress bar
					ProgressBar: &components.LinearProgressBar{
						Progress:      int32(percentComplete), // Progress as a ratio between 0 and 1
						ProgressColor: widget.GetBlockBackgroundColour("#00B899"),
						BarProperties: &properties.ContainerProperty{
							BgColor: widget.GetBlockBackgroundColour("#EFF2F6"),
							Size: &properties.Size{
								Height: &properties.Size_Dimension{
									ExactValue: 7,
								},
							},
							Corner: &properties.CornerProperty{
								TopLeftCornerRadius:  17,
								TopRightCornerRadius: 17,
								BottomLeftCorner:     17,
								BottomRightCorner:    17,
							},
						},
					},
					RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(lo.Ternary(int32(percentComplete) > 99, moneyPlantColorIcon, moneyPlantBlackAndWhiteIcon), 52, 52),
				},
			},
		},
	}

	return &dePb.FetchDynamicElementsResponse{
		Status:       rpc.StatusOk(),
		ElementsList: []*dePb.DynamicElement{dynamicElement},
	}, nil
}

// getDeeplinkForProgressBar generates the deeplink for a progress bar element
func getDeeplinkForProgressBar(progressBar *payConfig.ProgressBar) (*deeplinkPb.Deeplink, error) {
	if progressBar == nil || progressBar.Deeplink == "" {
		return nil, nil
	}

	screen := deeplinkPb.Screen(deeplinkPb.Screen_value[progressBar.Deeplink])

	// Handle specific screens that need custom deeplink generation
	switch screen {
	case deeplinkPb.Screen_LIST_ACCOUNT_PROVIDER_SCREEN:
		return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LIST_ACCOUNT_PROVIDER_SCREEN, &upiScreenOptions.ListAccountProviderScreenOptions{
			UpiAccountType: feUpiOnbEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_BANK_ACCOUNT,
		})
	case deeplinkPb.Screen_PAY_LANDING_SCREEN:
		return &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PAY_LANDING_SCREEN}, nil
	default:
		return &deeplinkPb.Deeplink{
			Screen: screen,
		}, nil
	}
}

// getRewardsTimeRange returns the time range for rewards data based on the configured time period
func getRewardsTimeRange(period string) (time.Time, time.Time) {
	now := time.Now()

	switch period {
	case RewardsPeriodLastMonth:
		// Last month
		firstDayOfMonth := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, now.Location())
		lastDayOfMonth := time.Date(now.Year(), now.Month(), 0, 23, 59, 59, *********, now.Location())
		return firstDayOfMonth, lastDayOfMonth

	case RewardsPeriodCurrentWeek:
		// Current week (starting from Monday)
		daysFromMonday := int(now.Weekday()) - int(time.Monday)
		if daysFromMonday < 0 {
			daysFromMonday += 7
		}
		monday := now.AddDate(0, 0, -daysFromMonday)
		startOfWeek := time.Date(monday.Year(), monday.Month(), monday.Day(), 0, 0, 0, 0, now.Location())
		return startOfWeek, now

	case RewardsPeriodCurrentMonth: // payConfig.RewardsPeriodCurrentMonth or any other value
		// Current month
		startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		return startOfMonth, now
	default:
		// Default to current month
		startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		return startOfMonth, now
	}
}

// getDeeplinkForBanner : generates the deeplink and corresponding screen options
// based on the screen type
func getDeeplinkForBanner(banner *payConfig.Banner) (*deeplinkPb.Deeplink, error) {
	if banner == nil {
		return nil, nil
	}

	screen := deeplinkPb.Screen(deeplinkPb.Screen_value[banner.Deeplink])

	switch screen {
	case deeplinkPb.Screen_LIST_ACCOUNT_PROVIDER_SCREEN:
		return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LIST_ACCOUNT_PROVIDER_SCREEN, &upiScreenOptions.ListAccountProviderScreenOptions{
			UpiAccountType: feUpiOnbEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_BANK_ACCOUNT,
		})
	case deeplinkPb.Screen_PAY_LANDING_SCREEN:
		return &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PAY_LANDING_SCREEN}, nil
	default:
		return &deeplinkPb.Deeplink{
			Screen: screen,
		}, nil
	}
}

// replacePlaceholders replaces placeholders in the format {placeholder} with their corresponding values from the map.
func replacePlaceholders(input string, values map[string]string) string {
	for placeholder, value := range values {
		input = strings.ReplaceAll(input, fmt.Sprintf("{%s}", placeholder), value)
	}
	return input
}

func (s *Service) getPlaceholderMap(data *bannerInfo) map[string]string {
	var placeholderMap = make(map[string]string)

	if data.tier != nil {
		tier := data.tier.GetTierInfo().GetTier()
		tierStr := tier.String()

		// Using config-driven maps instead of hardcoded ones
		if s.conf.TierConfig != nil {
			if iconUrl, ok := s.conf.TierConfig.TierToIconMap[tierStr]; ok {
				placeholderMap[TierIconUrlPlaceholder] = iconUrl
			}

			if cashbackFactor, exists := rewardsPkg.BeTierToCashbackFactorMap[tier]; exists {
				// Convert the factor (e.g., 0.02) to a percentage string (e.g., "2%")
				percentageVal := cashbackFactor * 100
				percentageStr := strconv.FormatFloat(percentageVal, 'f', 0, 64) + "%"
				placeholderMap[RewardsPercentagePlaceholder] = percentageStr
			}
		}
	}

	if data.rewardsAggr != nil && len(data.rewardsAggr.GetRewardOptionAggregates()) > 0 {
		placeholderMap[FiCoinsMonthlyEarnedPlaceholder] = strconv.FormatFloat(data.rewardsAggr.GetRewardOptionAggregates()[0].GetRewardUnits(), 'f', 0, 64)
	}

	return placeholderMap
}

// shouldShowBanner will be used to determine whether banner should be shown to a particular user based on feature flags or some other business logic.
// For Example - Here for UPI Mapper Quick Link Banner, we are sending `True` only if feature flag is enabled and also user hasn't linked his Vpa already.
func (s *Service) shouldShowBanner(ctx context.Context, deeplink string, actorId string) bool {
	switch deeplink {
	case "UPI_MAPPER_QUICK_LINK":
		isUpiMapperEnabled := fePkgUpi.IsUpiMapperEnabledForActor(ctx, actorId, s.upiOnboardingClient)
		isMapperQuickLinkBannerEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_UPI_QUICK_LINK_BANNER_PAY_LANDING_SCREEN).WithActorId(actorId))
		if err != nil {
			logger.Error(ctx, "error evaluating mapper quick link banner", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
			return false
		}
		if isUpiMapperEnabled && isMapperQuickLinkBannerEnabled && fePkgUpi.IsEligibleForMapperQuickLinkBanner(ctx, s.upiOnboardingClient, s.actorClient, actorId) {
			return true
		}
		return false
	default:
		// do nothing
	}
	return true
}
