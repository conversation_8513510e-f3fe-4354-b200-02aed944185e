---
description: When adding a script/processor in scripts/Pay/pay-god-script
globs: 
alwaysApply: false
---
# Pay God Script Processor Creation Rules

## Overview
This rule file helps create new processors for the pay-god-script following established patterns and best practices used across the codebase.

## **CRITICAL REQUIREMENTS** (Updated based on retrospective analysis)
⚠️ **These requirements are MANDATORY and must be followed exactly:**

1. **CSV Processing:** MUST use `github.com/gocarina/gocsv` with struct tags - NEVER use `encoding/csv`
2. **Concurrent Processing:** MANDATORY for >10 items OR external API calls - use goroutines + channels 
3. **🚨 CRITICAL - WaitGroup Race Condition:** ALWAYS call `wg.Add(1)` BEFORE `goroutine.RunWithCtx()` - NEVER inside goroutine
4. **Structured Logging:** PREFER `logger.Info/Error(ctx, msg, zap.Fields...)` over `log.Printf`
5. **Rate Limiting:** MUST use `go.uber.org/ratelimit` for external API calls
6. **Context Management:** MUST use `epificontext` with proper actor-id and trace-id
7. **Import Organization:** Follow the exact import requirements listed in Step 2

## Before You Start - Functionality Requirements

**Please answer these questions about your new processor:**

1. **Basic Information:**
   - What is the name of your new job? (e.g., "CHECK_ACTOR_ELIGIBILITY_FOR_CASHBACK")
   - What is the business purpose of this processor?

2. **Input Requirements:**
   - Do you need to parse JSON arguments from Args1? (y/n)
   - Do you need to parse JSON arguments from Args2? (y/n)
   - Do you need to read from a CSV file? (y/n)
   - Do you need additional file input via Args3/FileInput1? (y/n)

3. **Processing Requirements:**
   - Do you need to make gRPC calls to other services? If yes, which services?
   - Do you need database access? If yes, which type:
     - Direct EpifiDb connection via storagev2? If yes, how will you use it:
       - Raw SQL queries directly? (y/n)
       - Initialize DAOs with the connection? (y/n)
     - UsecaseProvider with CRDB resource map? (y/n)
   - Do you need Temporal workflow execution? (y/n)
   - Do you need S3 client for file operations? (y/n)
   - Do you need to process data in batches? (y/n)
   - Do you need rate limiting? (y/n)
   - Do you need context management (actor-id/trace-id)? (y/n)
   - If yes: Set trace-id outside loop (shared) or inside loop (unique per iteration)? (outside/inside)
   - **PROCESSING STRATEGY:** Do you need concurrent processing with goroutines? (y/n)
     - If processing >10 items OR making external API calls: **MANDATORY** use concurrent processing
     - If processing <10 items with no external calls: Sequential processing acceptable

4. **Output Requirements:**
   - Do you need to generate a CSV report? (y/n)
   - Do you need to send email reports? (y/n)
   - Do you need to save results to a file? (y/n)
   - Do you need to upload results to S3? (y/n)

## Implementation Steps

### Step 1: Add Job Type and Name
1. In `job_processor/job_processor.go`, add your new job type to the constants:
```go
const (
    // ... existing job types ...
    JobYourNewJobName JobType = [NEXT_NUMBER]
)
```

2. Add your job name to the JobNames map:
```go
var (
    JobNames = map[string]JobType{
        // ... existing jobs ...
        "YOUR_NEW_JOB_NAME": JobYourNewJobName,
    }
)
```

3. Add the case in `GetProcessorForJob` function:
```go
case JobYourNewJobName:
    processor, err, cleanupFn = NewYourNewJobProcessor(conf)
```

### Step 2: Create Processor File
Create `job_processor/your_new_job.go` with the following structure:

```go
package job_processor

import (
    "context"
    "encoding/json"
    "fmt"
    "log"
    
    // **MANDATORY IMPORTS based on requirements:**
    // For CSV processing: "github.com/gocarina/gocsv" (NEVER use encoding/csv - use gocsv ONLY)
    // For rate limiting: "go.uber.org/ratelimit"
    // For concurrent processing: "github.com/epifi/be-common/pkg/async/goroutine", "sync"
    // For structured logging: "github.com/epifi/be-common/pkg/logger", "go.uber.org/zap"
    // For context management: "google.golang.org/grpc/metadata", "github.com/epifi/be-common/pkg/epificontext"
    // For UUID generation: "github.com/google/uuid"
    // For email: commsPb "github.com/epifi/gamma/api/comms"
    // For gRPC: "github.com/epifi/be-common/pkg/epifigrpc"
    // For direct database: "gorm.io/gorm" and storagev2 "github.com/epifi/be-common/pkg/storage/v2"
    // For DAO with direct database: import your DAO package, e.g., "github.com/epifi/gamma/yourservice/dao"
    // For usecase database: "github.com/epifi/be-common/pkg/storage/v2/usecase"
    // For ID generator (if DAO needs it): "github.com/epifi/be-common/pkg/idgen"
    // For temporal: temporalClient "go.temporal.io/sdk/client", "github.com/epifi/be-common/pkg/epifitemporal/client"
    // For S3: "github.com/epifi/be-common/pkg/aws/v2/s3", awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
    
    "github.com/epifi/gamma/scripts/Pay/pay-god-script/config"
)

// YourNewJobProcessor handles [DESCRIBE PURPOSE]
type YourNewJobProcessor struct {
    // Add fields based on requirements:
    // jobArgs *yourNewJobArgs
    // rateLimiter ratelimit.Limiter (if rate limiting needed)
    // commsClient commsPb.CommsClient (if email needed)
    // yourServiceClient yourServicePb.YourServiceClient (if gRPC needed)
    // db *gorm.DB (if direct database connection with raw queries needed)
    // yourEpifiDao dao.YourEpifiDao (if direct database connection with DAO methods needed)
    // yourUsecaseDao dao.YourUsecaseDao (if usecase database needed)
    // temporalClient temporalClient.Client (if temporal needed)
    // s3Client s3.S3Client (if S3 operations needed)
}

// yourNewJobArgs defines the JSON structure for job arguments
type yourNewJobArgs struct {
    // Define based on your requirements, e.g.:
    // ActorIds []string `json:"actor_ids"`
    // FromDate string `json:"from_date"`
    // ToDate string `json:"to_date"`
    // ReceiverEmail string `json:"receiver_email"`
    // csvFilePath string // Set from Args3, not part of JSON
}

// NewYourNewJobProcessor creates a new processor with required dependencies
func NewYourNewJobProcessor(conf *config.Config) (*YourNewJobProcessor, error, func()) {
    // Initialize based on requirements:
    
    // For gRPC clients:
    // yourServiceConn := epifigrpc.NewConnByService(cfg.YOUR_SERVICE)
    
    // For direct database connection:
    // dbConn, err := storagev2.NewGormDB(conf.EpifiDb)
    // if err != nil {
    //     return nil, fmt.Errorf("failed to connect to database: %v", err), nil
    // }
    // 
    // // Option 1: Use for raw SQL queries directly
    // // (Use dbConn directly for db.Raw(), db.Exec(), etc.)
    // 
    // // Option 2: Initialize DAO with the connection for DAO methods
    // // yourEpifiDao := dao.NewYourEpifiDao(dbConn)
    
    // For usecase database connection:
    // crdbResourceMap, _, dbConnTeardown, err := usecase.NewDBResourceProvider(conf.UsecaseDbConfigMap, false, nil)
    // if err != nil {
    //     return nil, fmt.Errorf("error initialising DB Config map: %w", err), func() {}
    // }
    // 
    // // ID generator is optional - only add if your DAO requires it:
    // // clock := idgen.NewClock()
    // // domainIdGenerator := idgen.NewDomainIdGenerator(clock)
    // // yourDao := dao.NewYourDao(domainIdGenerator, crdbResourceMap)
    // 
    // // If your DAO doesn't need ID generator:
    // // yourDao := dao.NewYourDao(crdbResourceMap)
    
    // For temporal client:
    // temporalClient, err := epifitemporalClient.NewWorkflowClient(getEnvNamespaceName(namespace.YourNamespace, conf.Environment), true, conf.TemporalSecrets.TemporalCodecAesKey)
    // if err != nil {
    //     return nil, fmt.Errorf("error initialising temporal client: %w", err), func() {}
    // }
    
    // For S3 client:
    // awsConf, err := awsconfpkg.NewAWSConfig(context.Background(), conf.Aws.Region, false)
    // if err != nil {
    //     return nil, fmt.Errorf("error creating aws config: %w", err), func() {}
    // }
    // s3Client := s3.NewClient(awsConf, conf.YourS3Bucket)
    
    // For comms client:
    // commsConn := epifigrpc.NewConnByService(cfg.COMMS_SERVICE)
    
    cleanupFn := func() {
        // Add cleanup logic:
        // if yourServiceConn != nil {
        //     // nolint:errcheck,gosec
        //     yourServiceConn.Close()
        // }
        // if commsConn != nil {
        //     // nolint:errcheck,gosec
        //     commsConn.Close()
        // }
        // if dbConn != nil {
        //     sqlDB, _ := dbConn.DB()
        //     // nolint:errcheck,gosec
        //     sqlDB.Close()
        // }
        // if dbConnTeardown != nil {
        //     // nolint:errcheck,gosec
        //     dbConnTeardown()
        // }
        // if temporalClient != nil {
        //     // nolint:errcheck,gosec
        //     temporalClient.Close()
        // }
    }
    
    return &YourNewJobProcessor{
        // Initialize fields based on requirements:
        // db: dbConn, (if using raw queries)
        // yourEpifiDao: yourEpifiDao, (if using DAO with direct connection)
        // yourUsecaseDao: yourUsecaseDao, (if using usecase provider)
        // temporalClient: temporalClient,
        // s3Client: s3Client,
        // yourServiceClient: yourServicePb.NewYourServiceClient(yourServiceConn),
        // commsClient: commsPb.NewCommsClient(commsConn),
    }, nil, cleanupFn
}

// ParseAndStoreArgs parses and validates input arguments
// Expected input format:
// -JobName='YOUR_NEW_JOB_NAME' -Args1='{"key": "value"}' -Args2='{"key": "value"}' -Args3='/path/to/file.csv'
func (j *YourNewJobProcessor) ParseAndStoreArgs(input *JobRequest) error {
    // Parse Args1 if needed:
    // if input.Args1 != "" {
    //     if err := json.Unmarshal([]byte(input.Args1), &j.jobArgs); err != nil {
    //         return fmt.Errorf("failed to parse args1 JSON: %w", err)
    //     }
    // }
    
    // Parse Args2 if needed:
    // if input.Args2 != "" {
    //     // Parse additional arguments
    // }
    
    // Set file path from Args3 if needed:
    // if input.Args3 != "" {
    //     j.jobArgs.csvFilePath = input.Args3
    // }
    
    // Add validation logic:
    // if j.jobArgs.RequiredField == "" {
    //     return fmt.Errorf("required_field is required")
    // }
    
    return nil
}

// DoJob executes the main job logic
func (j *YourNewJobProcessor) DoJob(ctx context.Context) error {
    // **PREFERRED LOGGING:** Use structured logging with zap
    logger.Info(ctx, "Starting YourNewJob processor")
    // Alternative: log.Printf("Starting YourNewJob processor") // Only if zap not available
    
    // Add your business logic here
    
    // For CSV processing:
    // if j.jobArgs.csvFilePath != "" {
    //     data, err := j.readCSVFile(j.jobArgs.csvFilePath)
    //     if err != nil {
    //         return fmt.Errorf("failed to read CSV file: %w", err)
    //     }
    //     // Process data
    // }
    
    // For batch processing with rate limiting:
    // if j.rateLimiter != nil {
    //     for i := 0; i < len(data); i += batchSize {
    //         j.rateLimiter.Take()
    //         // Process batch
    //     }
    // }
    
    // For temporal workflow execution:
    // if j.jobArgs.ExecuteWorkflow {
    //     if err := j.executeTemporalWorkflow(ctx); err != nil {
    //         return fmt.Errorf("failed to execute temporal workflow: %w", err)
    //     }
    // }
    
    // For S3 file operations:
    // if j.jobArgs.S3InputFile != "" {
    //     fileContent, err := j.s3Client.Read(ctx, j.jobArgs.S3InputFile)
    //     if err != nil {
    //         return fmt.Errorf("failed to read S3 file: %w", err)
    //     }
    //     // Process fileContent
    // }
    
    // For email reporting:
    // if j.jobArgs.ReceiverEmail != "" {
    //     if err := j.sendEmailReport(ctx, reportData); err != nil {
    //         return fmt.Errorf("failed to send email report: %w", err)
    //     }
    // }
    
    // For S3 result upload:
    // if j.jobArgs.UploadToS3 {
    //     if err := j.uploadResultsToS3(ctx, resultData); err != nil {
    //         return fmt.Errorf("failed to upload results to S3: %w", err)
    //     }
    // }
    
    // **PREFERRED LOGGING:** Use structured logging with zap
    logger.Info(ctx, "Successfully completed YourNewJob processor")
    // Alternative: log.Printf("Successfully completed YourNewJob processor") // Only if zap not available
    return nil
}

// Additional helper methods based on requirements:

// **MANDATORY CSV PROCESSING PATTERN** - ALWAYS use gocsv, NEVER encoding/csv
// readCSVFile reads and parses CSV file (if CSV processing needed)
// func (j *YourNewJobProcessor) readCSVFile(filePath string) ([]YourDataType, error) {
//     file, err := os.Open(filePath)
//     if err != nil {
//         return nil, fmt.Errorf("failed to open CSV file: %w", err)
//     }
//     defer file.Close()
//     
//     var data []YourDataType
//     if err := gocsv.UnmarshalFile(file, &data); err != nil {
//         return nil, fmt.Errorf("failed to parse CSV file: %w", err)
//     }
//     
//     return data, nil
// }
//
// **CSV STRUCT TAGS:** Always use gocsv tags for CSV processing
// type YourDataType struct {
//     Column1 string `csv:"column1"`
//     Column2 string `csv:"column2"`
//     Column3 string `csv:"column3"`
// }

// sendEmailReport sends CSV report via email (if email reporting needed)
// func (j *YourNewJobProcessor) sendEmailReport(ctx context.Context, csvData []byte) error {
//     request := &commsPb.SendMessageRequest{
//         Type:   commsPb.QoS_GUARANTEED,
//         Medium: commsPb.Medium_EMAIL,
//         UserIdentifier: &commsPb.SendMessageRequest_EmailId{
//             EmailId: j.jobArgs.ReceiverEmail,
//         },
//         Message: &commsPb.SendMessageRequest_Email{
//             Email: &commsPb.EmailMessage{
//                 FromEmailId:   "<EMAIL>",
//                 FromEmailName: "Your Job Report",
//                 EmailOption: &commsPb.EmailOption{
//                     Option: &commsPb.EmailOption_InternalReportEmailOption{
//                         InternalReportEmailOption: &commsPb.InternalReportEmailOption{
//                             EmailType: commsPb.EmailType_INTERNAL_REPORT_EMAIL,
//                             Option: &commsPb.InternalReportEmailOption_InternalReportEmailOptionV1{
//                                 InternalReportEmailOptionV1: &commsPb.InternalReportEmailOptionsV1{
//                                     ReportTitle:     "Your Job Report",
//                                     ReportDate:      time.Now().Format("2006-01-02"),
//                                     TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
//                                 },
//                             },
//                         },
//                     },
//                 },
//                 Attachment: []*commsPb.EmailMessage_Attachment{
//                     {
//                         FileContent:    csvData,
//                         FileName:       "your_job_report.csv",
//                         Disposition:    commsPb.Disposition_ATTACHMENT,
//                         AttachmentType: "text/comma-separated-values",
//                     },
//                 },
//             },
//         },
//     }
//     
//     response, err := j.commsClient.SendMessage(ctx, request)
//     if err != nil || !response.GetStatus().IsSuccess() {
//         return fmt.Errorf("failed to send email: %w", err)
//     }
//     
//     return nil
// }

// processBatch processes data in batches (if batch processing needed)
// func (j *YourNewJobProcessor) processBatch(ctx context.Context, batch []YourDataType) error {
//     // Add batch processing logic
//     return nil
// }
```

### Step 3: Update Documentation
Add documentation for your new job in the README or relevant documentation:

```markdown
## YOUR_NEW_JOB_NAME
**Purpose:** [Describe what this job does]

**Usage:**
```bash
go run main.go -JobName='YOUR_NEW_JOB_NAME' -Args1='{"key": "value"}' -Args2='{"key": "value"}' -Args3='/path/to/file.csv'
```

**Arguments:**
- `Args1`: JSON string containing [describe Args1 structure]
- `Args2`: JSON string containing [describe Args2 structure] (optional)
- `Args3`: Path to CSV file containing [describe file format] (optional)

**Example:**
```bash
go run main.go -JobName='YOUR_NEW_JOB_NAME' -Args1='{"actor_ids": ["actor1", "actor2"], "email": "<EMAIL>"}'
```
```

### Step 4: Testing
1. Build your processor to check for compilation errors:
```bash
cd scripts/Pay/pay-god-script
go build .
```

2. Test with various input scenarios (unit tests recommended):
   - Valid input parsing
   - Invalid JSON handling
   - Missing required fields validation
   - File not found scenarios (if applicable)
   
**Note:** Running the binary requires all external dependencies (database, temporal, S3, etc.) to be available. Consider writing unit tests for individual methods instead.

## Best Practices

1. **Error Handling:** Always wrap errors with context using `fmt.Errorf("description: %w", err)`
2. **Logging:** **PREFERRED:** Use structured logging with `logger.Info/Error/Debug(ctx, "message", zap.Fields...)` 
   - Include context fields: `zap.String(logger.ACTOR_ID_V2, actorId)`, `zap.Error(err)`
   - **Fallback only:** Use `log.Printf` if structured logging not available
3. **CSV Processing:** **MANDATORY:** Use `github.com/gocarina/gocsv` with struct tags, NEVER `encoding/csv`
4. **Concurrent Processing:** **MANDATORY** for >10 items or external API calls using goroutines + channels
5. **🚨 CRITICAL - WaitGroup Race Condition:** **ALWAYS** call `wg.Add(1)` in main thread BEFORE starting goroutine
   - ✅ CORRECT: `wg.Add(1)` then `goroutine.RunWithCtx(...)`
   - ❌ WRONG: `wg.Add(1)` inside the goroutine (causes race condition)
6. **Validation:** Validate all inputs in `ParseAndStoreArgs`
7. **Cleanup:** Always implement proper cleanup in the constructor
8. **Rate Limiting:** Use rate limiting for external API calls with `go.uber.org/ratelimit`
9. **Comments:** Keep comments minimal and focused - unnecessary comments are NOT required unless specifically mentioned in patterns
10. **Documentation:** Add comprehensive comments explaining usage and arguments only where needed
11. **Testing:** Test edge cases and error scenarios

## Common Patterns

### **PROCESSING STRATEGY DECISION MATRIX:**
- **Concurrent Processing (MANDATORY):**
  - Processing >10 items
  - Making external API/gRPC calls  
  - Rate limiting required
  - Use: `github.com/epifi/be-common/pkg/async/goroutine` + channels + `go.uber.org/ratelimit`

- **Sequential Processing (Only for simple cases):**
  - Processing <10 items
  - No external calls
  - Simple data transformations only

### **MANDATORY CSV Processing Pattern** - Use gocsv ONLY, NEVER encoding/csv
```go
type CSVRow struct {
    Column1 string `csv:"column1"`
    Column2 string `csv:"column2"`
}

func (j *YourProcessor) readCSV(filePath string) ([]CSVRow, error) {
    file, err := os.Open(filePath)
    if err != nil {
        return nil, fmt.Errorf("failed to open CSV: %w", err)
    }
    defer file.Close()
    
    var rows []CSVRow
    if err := gocsv.UnmarshalFile(file, &rows); err != nil {
        return nil, fmt.Errorf("failed to parse CSV: %w", err)
    }
    
    return rows, nil
}
```

### **MANDATORY:** Rate Limiting with Goroutines and Channels (Required for >10 items OR external API calls)
```go
import (
    "go.uber.org/ratelimit"
    "sync"
    "google.golang.org/grpc/metadata"
    "github.com/epifi/be-common/pkg/async/goroutine"
    "github.com/epifi/be-common/pkg/epificontext"
)

// Rate limiter setup
const (
    rateLimit         = 9
    rateLimitDuration = 2 * time.Second
)

func (j *YourProcessor) processWithRateLimit(ctx context.Context, items []YourItem) error {
    rl := ratelimit.New(rateLimit, ratelimit.Per(rateLimitDuration))
    
    var wg sync.WaitGroup
    resultsCh := make(chan result, len(items))
    
    // Process items concurrently with rate limiting
    for idx, item := range items {
        // Capture loop variables
        idx := idx
        item := item
        
        // **CRITICAL:** Add to wait group BEFORE starting goroutine to avoid race conditions
        wg.Add(1)
        
        goroutine.RunWithCtx(ctx, func(ctx context.Context) {
            // nocustomlint:waitgroup
            defer wg.Done()
            
            // Rate limit INSIDE the goroutine - this is key!
            rl.Take()
            
            // Process the item
            if err := j.processSingleItem(ctx, item); err != nil {
                resultsCh <- result{idx: idx, item: item, isSuccess: false, error: err}
                return
            }
            resultsCh <- result{idx: idx, item: item, isSuccess: true}
        })
    }
    
    // Close channel when all goroutines complete
    goroutine.RunWithCtx(ctx, func(ctx context.Context) {
        // nocustomlint:waitgroup
        wg.Wait()
        close(resultsCh)
    })
    
    // Process results
    var failedItems []YourItem
    for res := range resultsCh {
        if !res.isSuccess {
            failedItems = append(failedItems, res.item)
            logFields := []zap.Field{zap.Error(res.error)}
            // Log available identifiers for better debugging (adapt based on your item structure)
            if res.item.ActorId != "" {
                logFields = append(logFields, zap.String(logger.ACTOR_ID_V2, res.item.ActorId))
            } else if res.item.SomeOtherIdField != "" { // Replace with your actual identifier field
                logFields = append(logFields, zap.String("identifier_field", res.item.SomeOtherIdField))
            }
            // Add item index as fallback identifier for concurrent processing
            logFields = append(logFields, zap.Int("item_index", res.idx))
            logger.Error(ctx, "failed to process item", logFields...)
        }
    }
    
    if len(failedItems) > 0 {
        logger.Error(ctx, "some items failed processing", zap.Int("failed_count", len(failedItems)))
    }
    
    return nil
}

// Result structure for channel communication
type result struct {
    idx       int
    item      YourItem
    result    YourResult
    isSuccess bool
    error     error
}
```

### Context Management (Actor ID and Trace ID)
```go
import (
    "google.golang.org/grpc/metadata"
    "github.com/epifi/be-common/pkg/epificontext"
)

// Question: Do you want to set trace-id outside the loop (same for all) or inside the loop (unique per iteration)?
// This will be prompted during processor creation.

func (j *YourProcessor) processWithContext(ctx context.Context, items []YourItem) error {
    // Option 1: Set trace-id OUTSIDE the loop (same trace-id for all iterations)
    // processCtx := epificontext.WithTraceId(ctx, metadata.MD{})
    // for _, item := range items {
    //     itemCtx := processCtx
    //     if item.ActorId != "" { // Actor-id is optional
    //         itemCtx = epificontext.CtxWithActorId(processCtx, item.ActorId)
    //     }
    //     // ... process item with itemCtx
    // }
    
    // Option 2: Set trace-id INSIDE the loop (unique trace-id per iteration)
    for _, item := range items {
        // Create a new context from parent context with trace_id and actor_id for each item
        itemCtx := epificontext.WithTraceId(ctx, metadata.MD{})
        if item.ActorId != "" { // Actor-id is optional
            itemCtx = epificontext.CtxWithActorId(itemCtx, item.ActorId)
        }
        
        err := j.processSingleItem(itemCtx, item)
        if err != nil {
            logFields := []zap.Field{zap.Error(err)}
            // Log available identifiers for better debugging (adapt based on your item structure)
            if item.ActorId != "" {
                logFields = append(logFields, zap.String(logger.ACTOR_ID_V2, item.ActorId))
            } else if item.SomeOtherIdField != "" { // Replace with your actual identifier field
                logFields = append(logFields, zap.String("identifier_field", item.SomeOtherIdField))
            }
            logger.Error(itemCtx, "error processing item", logFields...)
            continue
        }
        
        logFields := []zap.Field{}
        // Log available identifiers for successful processing (adapt based on your item structure)
        if item.ActorId != "" {
            logFields = append(logFields, zap.String(logger.ACTOR_ID_V2, item.ActorId))
        } else if item.SomeOtherIdField != "" { // Replace with your actual identifier field
            logFields = append(logFields, zap.String("identifier_field", item.SomeOtherIdField))
        }
        logger.Info(itemCtx, "successfully processed item", logFields...)
    }
    return nil
}

// For concurrent processing with context
func (j *YourProcessor) processWithContextAndRateLimit(ctx context.Context, items []YourItem) error {
    rl := ratelimit.New(5) // Adjust rate as needed
    
    var wg sync.WaitGroup
    resultsCh := make(chan result, len(items))
    
    // Option 1: Set trace-id OUTSIDE the loop (same for all iterations)
    // processCtx := epificontext.WithTraceId(ctx, metadata.MD{})
    
    for idx, item := range items {
        // Capture loop variables
        idx := idx
        item := item
        
        // **CRITICAL:** Add to wait group BEFORE starting goroutine to avoid race conditions
        wg.Add(1)
        
        goroutine.RunWithCtx(ctx, func(ctx context.Context) {
            // nocustomlint:waitgroup
            defer wg.Done()
            
            rl.Take()
            
            // Option 1: Use shared trace-id (uncomment if trace-id set outside loop)
            // itemCtx := processCtx
            // if item.ActorId != "" { // Actor-id is optional
            //     itemCtx = epificontext.CtxWithActorId(processCtx, item.ActorId)
            // }
            
            // Option 2: Set unique trace-id per iteration (inside loop)
            itemCtx := epificontext.WithTraceId(ctx, metadata.MD{})
            if item.ActorId != "" { // Actor-id is optional
                itemCtx = epificontext.CtxWithActorId(itemCtx, item.ActorId)
            }
            
            if err := j.processSingleItem(itemCtx, item); err != nil {
                resultsCh <- result{idx: idx, item: item, isSuccess: false, error: err}
                return
            }
            resultsCh <- result{idx: idx, item: item, isSuccess: true}
        })
    }
    
    goroutine.RunWithCtx(ctx, func(ctx context.Context) {
        // nocustomlint:waitgroup
        wg.Wait()
        close(resultsCh)
    })
    
    // Process results
    var failedItems []YourItem
    for res := range resultsCh {
        if !res.isSuccess {
            failedItems = append(failedItems, res.item)
            logFields := []zap.Field{zap.Error(res.error)}
            // Log available identifiers for better debugging (adapt based on your item structure)
            if res.item.ActorId != "" {
                logFields = append(logFields, zap.String(logger.ACTOR_ID_V2, res.item.ActorId))
            } else if res.item.SomeOtherIdField != "" { // Replace with your actual identifier field
                logFields = append(logFields, zap.String("identifier_field", res.item.SomeOtherIdField))
            }
            // Add item index as fallback identifier for concurrent processing
            logFields = append(logFields, zap.Int("item_index", res.idx))
            logger.Error(ctx, "failed to process item", logFields...)
        }
    }
    
    if len(failedItems) > 0 {
        logger.Error(ctx, "some items failed processing", zap.Int("failed_count", len(failedItems)))
    }
    
    return nil
}
```

### Batch Processing
```go
const batchSize = 100

for i := 0; i < len(data); i += batchSize {
    end := i + batchSize
    if end > len(data) {
        end = len(data)
    }
    
    batch := data[i:end]
    if err := j.processBatch(ctx, batch); err != nil {
        return fmt.Errorf("failed to process batch %d-%d: %w", i, end, err)
    }
    
    // Optional: Add delay between batches
    time.Sleep(time.Second)
}
```

### Email Reporting
```go
func (j *YourProcessor) sendReport(ctx context.Context, reportData []byte, recipient string) error {
    request := &commsPb.SendMessageRequest{
        Type:   commsPb.QoS_GUARANTEED,
        Medium: commsPb.Medium_EMAIL,
        UserIdentifier: &commsPb.SendMessageRequest_EmailId{
            EmailId: recipient,
        },
        Message: &commsPb.SendMessageRequest_Email{
            Email: &commsPb.EmailMessage{
                FromEmailId:   "<EMAIL>",
                FromEmailName: "Your Processor Report",
                EmailOption: &commsPb.EmailOption{
                    Option: &commsPb.EmailOption_InternalReportEmailOption{
                        InternalReportEmailOption: &commsPb.InternalReportEmailOption{
                            EmailType: commsPb.EmailType_INTERNAL_REPORT_EMAIL,
                            Option: &commsPb.InternalReportEmailOption_InternalReportEmailOptionV1{
                                InternalReportEmailOptionV1: &commsPb.InternalReportEmailOptionsV1{
                                    ReportTitle:     "Your Report Title",
                                    ReportDate:      time.Now().Format("2006-01-02"),
                                    TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
                                },
                            },
                        },
                    },
                },
                Attachment: []*commsPb.EmailMessage_Attachment{
                    {
                        FileContent:    reportData,
                        FileName:       "report.csv",
                        Disposition:    commsPb.Disposition_ATTACHMENT,
                        AttachmentType: "text/comma-separated-values",
                    },
                },
            },
        },
    }
    
    response, err := j.commsClient.SendMessage(ctx, request)
    if err != nil || !response.GetStatus().IsSuccess() {
        return fmt.Errorf("failed to send email: %w", err)
    }
    
    return nil
}
```

### Temporal Workflow Execution
```go
// executeTemporalWorkflow executes a temporal workflow (if temporal needed)
func (j *YourProcessor) executeTemporalWorkflow(ctx context.Context) error {
    // Create workflow payload
    workflowPayload := &payload.YourWorkflowPayload{
        // Set your payload fields
        Field1: j.jobArgs.Field1,
        Field2: j.jobArgs.Field2,
    }
    
    // Set workflow options
    options := &temporalClient.StartWorkflowOptions{
        ID: fmt.Sprintf("your-workflow-%s-%s", j.jobArgs.SomeId, uuid.New().String()),
    }
    
    // Execute workflow
    run, err := workflow.ExecuteAsync(ctx, j.temporalClient, payNs.YourWorkflow, workflowPayload, options)
    if err != nil {
        return fmt.Errorf("error initiating workflow execution: %w", err)
    }
    
    log.Printf("Successfully initiated workflow execution: workflow_id: %s", run.GetID())
    return nil
}

// Helper function for environment namespace (if temporal needed)
func getEnvNamespaceName(ns epifitemporal.Namespace, env string) string {
    if cfg.IsLocalEnv(env) {
        return strings.ToLower(string(ns))
    }
    return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}
```

### Database Operations

#### Direct Database Connection - Raw Queries
```go
// processDatabaseRecords processes records using raw SQL queries
func (j *YourProcessor) processDatabaseRecords(ctx context.Context) error {
    // Direct SQL query example
    query := "SELECT id, field1, field2 FROM your_table WHERE condition = ?"
    
    var records []YourRecord
    if err := j.db.Raw(query, j.jobArgs.Condition).Scan(&records).Error; err != nil {
        return fmt.Errorf("failed to fetch records: %w", err)
    }
    
    // Process records in batches
    const batchSize = 100
    for i := 0; i < len(records); i += batchSize {
        end := i + batchSize
        if end > len(records) {
            end = len(records)
        }
        
        batch := records[i:end]
        if err := j.processBatch(ctx, batch); err != nil {
            return fmt.Errorf("failed to process batch %d-%d: %w", i, end, err)
        }
    }
    
    return nil
}

// updateDatabaseRecords updates records using direct database connection
func (j *YourProcessor) updateDatabaseRecords(ctx context.Context, updates []YourUpdate) error {
    // Bulk update example
    query := "UPDATE your_table SET field1 = ?, updated_at = NOW() WHERE id IN (?)"
    
    var ids []string
    for _, update := range updates {
        ids = append(ids, update.ID)
    }
    
    result := j.db.Exec(query, j.jobArgs.NewValue, ids)
    if result.Error != nil {
        return fmt.Errorf("failed to execute bulk update: %w", result.Error)
    }
    
    log.Printf("Updated %d records", result.RowsAffected)
    return nil
}
```

#### Direct Database Connection - DAO Methods
```go
// processWithEpifiDao processes data using DAO methods with direct database connection
func (j *YourProcessor) processWithEpifiDao(ctx context.Context) error {
    // Use DAO methods instead of raw queries
    records, err := j.yourEpifiDao.GetRecordsByCondition(ctx, j.jobArgs.Condition)
    if err != nil {
        return fmt.Errorf("failed to fetch records using DAO: %w", err)
    }
    
    // Process records in batches
    const batchSize = 100
    for i := 0; i < len(records); i += batchSize {
        end := i + batchSize
        if end > len(records) {
            end = len(records)
        }
        
        batch := records[i:end]
        if err := j.processBatch(ctx, batch); err != nil {
            return fmt.Errorf("failed to process batch %d-%d: %w", i, end, err)
        }
    }
    
    return nil
}

// updateWithEpifiDao updates records using DAO methods
func (j *YourProcessor) updateWithEpifiDao(ctx context.Context, updates []YourUpdate) error {
    // Use DAO method for bulk updates
    affectedRows, err := j.yourEpifiDao.BulkUpdateRecords(ctx, updates)
    if err != nil {
        return fmt.Errorf("failed to execute bulk update using DAO: %w", err)
    }
    
    log.Printf("Updated %d records using DAO", affectedRows)
    return nil
}

// createWithEpifiDao creates records using DAO methods
func (j *YourProcessor) createWithEpifiDao(ctx context.Context, records []YourRecord) error {
    // Use DAO method for bulk insert
    createdRecords, err := j.yourEpifiDao.BulkCreateRecords(ctx, records)
    if err != nil {
        return fmt.Errorf("failed to create records using DAO: %w", err)
    }
    
    log.Printf("Created %d records using DAO", len(createdRecords))
    return nil
}
```

#### UseCase Provider Database Connection
```go
// processWithUsecaseDao processes data using usecase DAO pattern
func (j *YourProcessor) processWithUsecaseDao(ctx context.Context) error {
    // Set ownership context for multi-tenant scenarios
    ctx = epificontext.WithOwnership(ctx, j.jobArgs.Ownership)
    
    // Use pagination for large datasets
    pageToken := &pagination.PageToken{
        Timestamp: timestampPb.New(j.jobArgs.StartTime),
        Offset:    0,
        IsReverse: false,
    }
    
    pageCtxResp := &rpc.PageContextResponse{HasAfter: true}
    
    for pageNumber := 0; pageCtxResp.GetHasAfter(); pageNumber++ {
        // Fetch data using DAO
        records, pageResp, err := j.yourDao.GetRecords(ctx, pageToken, 100, dao.WithFilter(j.jobArgs.Filter))
        if err != nil {
            return fmt.Errorf("error fetching records page %d: %w", pageNumber, err)
        }
        
        // Process the records
        if err := j.processRecordsBatch(ctx, records); err != nil {
            return fmt.Errorf("error processing records page %d: %w", pageNumber, err)
        }
        
        // Get next page token
        pageToken, err = pagination.GetPageToken(&rpc.PageContextRequest{
            Token:    &rpc.PageContextRequest_AfterToken{AfterToken: pageResp.GetAfterToken()},
            PageSize: 100,
        })
        if err != nil {
            return fmt.Errorf("error generating next page token: %w", err)
        }
        
        pageCtxResp = pageResp
        log.Printf("Processed page %d with %d records", pageNumber, len(records))
    }
    
    return nil
}
```

### S3 Operations
```go
// readFromS3 reads file content from S3 (if S3 needed)
func (j *YourProcessor) readFromS3(ctx context.Context, filename string) ([]byte, error) {
    content, err := j.s3Client.Read(ctx, filename)
    if err != nil {
        return nil, fmt.Errorf("failed to read file from S3: %w", err)
    }
    return content, nil
}

// uploadResultsToS3 uploads results to S3 (if S3 upload needed)
func (j *YourProcessor) uploadResultsToS3(ctx context.Context, data []byte) error {
    // Generate filename with timestamp
    filename := fmt.Sprintf("results/%s_%s.csv", 
        j.jobArgs.JobName, 
        time.Now().Format("2006-01-02_15-04-05"))
    
    // Upload to S3
    if err := j.s3Client.Write(ctx, filename, data, "bucket-owner-full-control"); err != nil {
        return fmt.Errorf("failed to upload results to S3: %w", err)
    }
    
    log.Printf("Results uploaded to S3: %s", filename)
    return nil
}

// processS3CSVFile processes CSV file from S3
func (j *YourProcessor) processS3CSVFile(ctx context.Context, filename string) error {
    // Read CSV content from S3
    csvContent, err := j.s3Client.Read(ctx, filename)
    if err != nil {
        return fmt.Errorf("failed to read CSV from S3: %w", err)
    }
    
    // **MANDATORY:** Use gocsv library for CSV parsing - NEVER use encoding/csv
    var records []YourDataType
    if err := gocsv.UnmarshalBytes(csvContent, &records); err != nil {
        return fmt.Errorf("failed to parse CSV: %w", err)
    }
    
    // Process records
    for i, record := range records {
        // Process individual record
        if err := j.processCSVRecord(ctx, record); err != nil {
            log.Printf("Error processing record %d: %v", i, err)
            continue
        }
    }
    
    return nil
}

// generateAndUploadCSVReport generates CSV report and uploads to S3
func (j *YourProcessor) generateAndUploadCSVReport(ctx context.Context, data []YourDataType) error {
    // **MANDATORY:** Use gocsv library for CSV generation - NEVER use encoding/csv
    csvBytes, err := gocsv.MarshalBytes(data)
    if err != nil {
        return fmt.Errorf("error marshaling data to CSV: %w", err)
    }
    
    // Upload to S3
    return j.uploadResultsToS3(ctx, csvBytes)
}
```

Remember to focus on your business logic - the patterns above handle the infrastructure concerns! 
