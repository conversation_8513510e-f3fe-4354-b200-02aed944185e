// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	"net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	gorm "gorm.io/gorm"

	celestialpb "github.com/epifi/be-common/api/celestial"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	cache "github.com/epifi/be-common/pkg/cache"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	types "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	errgroup "github.com/epifi/be-common/pkg/errgroup"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	balancepb "github.com/epifi/gamma/api/accounts/balance"
	actor "github.com/epifi/gamma/api/actor"
	amlpb "github.com/epifi/gamma/api/aml"
	investment "github.com/epifi/gamma/api/analyser/investment"
	txnaggregatespb "github.com/epifi/gamma/api/analyser/txnaggregates"
	variablespb "github.com/epifi/gamma/api/analyser/variables"
	authpb "github.com/epifi/gamma/api/auth"
	authlocationpb "github.com/epifi/gamma/api/auth/location"
	provisioning "github.com/epifi/gamma/api/card/provisioning"
	evrpb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	categorizerpb "github.com/epifi/gamma/api/categorizer"
	collection "github.com/epifi/gamma/api/collection"
	commspb "github.com/epifi/gamma/api/comms"
	capb "github.com/epifi/gamma/api/connected_account"
	aaanalyticspb "github.com/epifi/gamma/api/connected_account/analytics"
	consent "github.com/epifi/gamma/api/consent"
	creditreport "github.com/epifi/gamma/api/creditreportv2"
	callivrpb "github.com/epifi/gamma/api/cx/call_ivr"
	callroutingpb "github.com/epifi/gamma/api/cx/call_routing"
	cxlivechatfbkpb "github.com/epifi/gamma/api/cx/chat/bot/livechatfallback"
	cxchatbotworkflowpb "github.com/epifi/gamma/api/cx/chat/bot/workflow"
	dpb "github.com/epifi/gamma/api/cx/dispute"
	federalpb "github.com/epifi/gamma/api/cx/federal"
	sprinklrpb "github.com/epifi/gamma/api/cx/sprinklr"
	ticketpb "github.com/epifi/gamma/api/cx/ticket"
	depositpb "github.com/epifi/gamma/api/deposit"
	fireflypb "github.com/epifi/gamma/api/firefly"
	ffaccpb "github.com/epifi/gamma/api/firefly/accounting"
	ffbillingpb "github.com/epifi/gamma/api/firefly/billing"
	fireflyv2pb "github.com/epifi/gamma/api/firefly/v2"
	issuereporting "github.com/epifi/gamma/api/inapphelp/issue_reporting"
	beepfpb "github.com/epifi/gamma/api/insights/epf"
	networthpb "github.com/epifi/gamma/api/insights/networth"
	userdeclarationpb "github.com/epifi/gamma/api/insights/user_declaration"
	catalogpb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	leadspb "github.com/epifi/gamma/api/leads"
	leadsexternalpb "github.com/epifi/gamma/api/leads/external"
	merchantpb "github.com/epifi/gamma/api/merchant"
	orderpb "github.com/epifi/gamma/api/order"
	panpb "github.com/epifi/gamma/api/pan"
	pay "github.com/epifi/gamma/api/pay"
	paymentinstrumentpb "github.com/epifi/gamma/api/paymentinstrument"
	accountpipb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	preapprovedloan "github.com/epifi/gamma/api/preapprovedloan"
	managerpb "github.com/epifi/gamma/api/quest/manager"
	recurringpay "github.com/epifi/gamma/api/recurringpayment"
	rewardspb "github.com/epifi/gamma/api/rewards"
	salarypb "github.com/epifi/gamma/api/salaryprogram"
	healthinsurancepb "github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	savingspb "github.com/epifi/gamma/api/savings"
	segmentpb "github.com/epifi/gamma/api/segment"
	tspamlpb "github.com/epifi/gamma/api/tsp/aml"
	tspauthpb "github.com/epifi/gamma/api/tsp/auth"
	tspcommspb "github.com/epifi/gamma/api/tsp/comms"
	consent3 "github.com/epifi/gamma/api/tsp/consent"
	mandate2 "github.com/epifi/gamma/api/tsp/mandate"
	payment2 "github.com/epifi/gamma/api/tsp/payment"
	paymentinstrument2 "github.com/epifi/gamma/api/tsp/paymentinstrument"
	pennydrop2 "github.com/epifi/gamma/api/tsp/pennydrop"
	tsppqueuepb "github.com/epifi/gamma/api/tsp/persistentqueue"
	tspuserpb2 "github.com/epifi/gamma/api/tsp/user"
	tspuserpb "github.com/epifi/gamma/api/tspuser"
	upionbpb "github.com/epifi/gamma/api/upi/onboarding"
	user "github.com/epifi/gamma/api/user"
	usergrouppb "github.com/epifi/gamma/api/user/group"
	userlocationpb "github.com/epifi/gamma/api/user/location"
	obfuscator "github.com/epifi/gamma/api/user/obfuscator"
	onbpb "github.com/epifi/gamma/api/user/onboarding"
	usstockscatalogpb "github.com/epifi/gamma/api/usstocks/catalog"
	vgaapb "github.com/epifi/gamma/api/vendorgateway/aa"
	fennelvgpb "github.com/epifi/gamma/api/vendorgateway/fennel"
	creditcardvgpb "github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	vglivenesspb "github.com/epifi/gamma/api/vendorgateway/liveness"
	vglocationpb "github.com/epifi/gamma/api/vendorgateway/location"
	vgnamecheckpb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	vgocrpb "github.com/epifi/gamma/api/vendorgateway/ocr"
	vgscienapticpb "github.com/epifi/gamma/api/vendorgateway/scienaptic"
	vgsmspb "github.com/epifi/gamma/api/vendorgateway/sms"
	vgvkyccallpb "github.com/epifi/gamma/api/vendorgateway/vkyccall"
	vendormappingpb "github.com/epifi/gamma/api/vendormapping"
	aa2 "github.com/epifi/gamma/api/vendornotification/aa"
	igvnpb "github.com/epifi/gamma/api/vendornotification/aa/analytics/ignosis"
	tss2 "github.com/epifi/gamma/api/vendornotification/aml/tss"
	authpb2 "github.com/epifi/gamma/api/vendornotification/auth"
	shipwaycardpb "github.com/epifi/gamma/api/vendornotification/card/shipway"
	unsubscribe "github.com/epifi/gamma/api/vendornotification/comms/unsubscribe"
	ccpb "github.com/epifi/gamma/api/vendornotification/creditcard"
	paisabazaar2 "github.com/epifi/gamma/api/vendornotification/creditcard/paisabazaar"
	savenvnpb "github.com/epifi/gamma/api/vendornotification/creditcard/saven"
	chatbotauthpb "github.com/epifi/gamma/api/vendornotification/cx/chatbot/auth/senseforth"
	freshchat3 "github.com/epifi/gamma/api/vendornotification/cx/chatbot/inapphelp/freshchat"
	senseforth2 "github.com/epifi/gamma/api/vendornotification/cx/chatbot/livechatfallback/senseforth"
	nugget2 "github.com/epifi/gamma/api/vendornotification/cx/chatbot/nugget"
	vnchatbotworkflowpb "github.com/epifi/gamma/api/vendornotification/cx/chatbot/workflow/senseforth"
	cxescalationpb "github.com/epifi/gamma/api/vendornotification/cx/federal"
	freshchat2 "github.com/epifi/gamma/api/vendornotification/cx/freshchat"
	ozonetel2 "github.com/epifi/gamma/api/vendornotification/cx/ozonetel"
	sprinklrpb2 "github.com/epifi/gamma/api/vendornotification/cx/sprinklr"
	email2 "github.com/epifi/gamma/api/vendornotification/email"
	epanvnpb "github.com/epifi/gamma/api/vendornotification/epan/karza"
	ficoinsaccvnpb2 "github.com/epifi/gamma/api/vendornotification/fi_coins_accounting"
	riskcovryvnpb "github.com/epifi/gamma/api/vendornotification/healthinsurance/riskcovry"
	smallcase2 "github.com/epifi/gamma/api/vendornotification/investment/smallcase"
	axisekyc2 "github.com/epifi/gamma/api/vendornotification/kyc/axis"
	vnidfcpb "github.com/epifi/gamma/api/vendornotification/kyc/idfc"
	inhousebrevnpb "github.com/epifi/gamma/api/vendornotification/lending/bre/inhouse"
	credgenicsvnpb "github.com/epifi/gamma/api/vendornotification/lending/credgenics"
	abfl "github.com/epifi/gamma/api/vendornotification/lending/loans/abfl"
	lendingfederalpb "github.com/epifi/gamma/api/vendornotification/lending/loans/federal"
	fiftyfinvnpb "github.com/epifi/gamma/api/vendornotification/lending/loans/fiftyfin"
	moneyview "github.com/epifi/gamma/api/vendornotification/lending/loans/moneyview"
	setu "github.com/epifi/gamma/api/vendornotification/lending/setu"
	karza2 "github.com/epifi/gamma/api/vendornotification/liveness/karza"
	moengagevnpb "github.com/epifi/gamma/api/vendornotification/notifications/moengage"
	offersvnpb2 "github.com/epifi/gamma/api/vendornotification/offers/externalredemptions"
	axisaccounts2 "github.com/epifi/gamma/api/vendornotification/openbanking/accounts/axis"
	federalaccountspb "github.com/epifi/gamma/api/vendornotification/openbanking/accounts/federal"
	federalauthpb "github.com/epifi/gamma/api/vendornotification/openbanking/auth/federal"
	federalcardpb "github.com/epifi/gamma/api/vendornotification/openbanking/card/federal"
	federaldepositpb "github.com/epifi/gamma/api/vendornotification/openbanking/deposit/federal"
	federaldmppb "github.com/epifi/gamma/api/vendornotification/openbanking/dispute"
	federalkyctypechangepb "github.com/epifi/gamma/api/vendornotification/openbanking/kyctypechange/federal"
	axispayment2 "github.com/epifi/gamma/api/vendornotification/openbanking/payment/axis"
	federalpaymentpb "github.com/epifi/gamma/api/vendornotification/openbanking/payment/federal"
	rpfederalvnpb "github.com/epifi/gamma/api/vendornotification/openbanking/recurringpayment/federal"
	federalshippingaddressupdatepb "github.com/epifi/gamma/api/vendornotification/openbanking/shipping_preference/federal"
	razorpayvnpb "github.com/epifi/gamma/api/vendornotification/paymentgateway/razorpay"
	gupshuprcsvnpb "github.com/epifi/gamma/api/vendornotification/rcs/gupshup"
	rewardpb "github.com/epifi/gamma/api/vendornotification/reward"
	aclpb "github.com/epifi/gamma/api/vendornotification/sms/acl"
	airtelvnpb "github.com/epifi/gamma/api/vendornotification/sms/airtel"
	kaleyrapb "github.com/epifi/gamma/api/vendornotification/sms/kaleyra"
	netcorevnpb "github.com/epifi/gamma/api/vendornotification/sms/netcore"
	videosdk2 "github.com/epifi/gamma/api/vendornotification/videocall/videosdk"
	karza4 "github.com/epifi/gamma/api/vendornotification/vkyc/karza"
	aclwapb "github.com/epifi/gamma/api/vendornotification/whatsapp/acl"
	airtelwpvnpb "github.com/epifi/gamma/api/vendornotification/whatsapp/airtel"
	gupshupvnpb "github.com/epifi/gamma/api/vendornotification/whatsapp/gupshup"
	wire2 "github.com/epifi/gamma/leads/external/wire"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	servergenwire "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	questsdkinit "github.com/epifi/gamma/quest/sdk/init"
	tspconf "github.com/epifi/gamma/tsp/config"
	wire3 "github.com/epifi/gamma/tsp/wire"
	vendornotificationconf "github.com/epifi/gamma/vendornotification/config"
	genconf2 "github.com/epifi/gamma/vendornotification/config/genconf"
	servergenhook "github.com/epifi/gamma/vendornotification/servergenhook"
	wire "github.com/epifi/gamma/vendornotification/wire"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.VENDOR_NOTIFI_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.VENDOR_NOTIFI_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.VENDOR_NOTIFI_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.VENDOR_NOTIFI_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	attributeRateLimiter := ratelimiter.NewAttributeRateLimiter(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcAttributeRatelimiterParams().Namespace())
	universalConn := epifigrpc.NewServerConn(cfg.VENDOR_NOTIFI_SERVER, cfg.UNIVERSAL_SERVER)
	defer epifigrpc.CloseConn(universalConn)
	managerClient := managerpb.NewManagerClient(universalConn)
	onboardingConn := epifigrpc.NewServerConn(cfg.VENDOR_NOTIFI_SERVER, cfg.ONBOARDING_SERVER)
	defer epifigrpc.CloseConn(onboardingConn)
	groupClient := usergrouppb.NewGroupClient(onboardingConn)
	usersClient := user.NewUsersClient(onboardingConn)
	payConn := epifigrpc.NewServerConn(cfg.VENDOR_NOTIFI_SERVER, cfg.PAY_SERVER)
	defer epifigrpc.CloseConn(payConn)
	actorClient := actor.NewActorClient(payConn)
	segmentationServiceClient := segmentpb.NewSegmentationServiceClient(universalConn)
	orderServiceClient := orderpb.NewOrderServiceClient(payConn)
	connectedAccountClient := capb.NewConnectedAccountClient(universalConn)
	vendorgatewayConn := epifigrpc.NewServerConn(cfg.VENDOR_NOTIFI_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	accountAggregatorClient := vgaapb.NewAccountAggregatorClient(vendorgatewayConn)
	callRoutingClient := callroutingpb.NewCallRoutingClient(universalConn)
	ivrClient := callivrpb.NewIvrClient(universalConn)
	authClient := authpb.NewAuthClient(onboardingConn)
	liveChatFallbackClient := cxlivechatfbkpb.NewLiveChatFallbackClient(universalConn)
	workflowClient := cxchatbotworkflowpb.NewWorkflowClient(universalConn)
	healthInsuranceClient := healthinsurancepb.NewHealthInsuranceClient(universalConn)
	disputeClient := dpb.NewDisputeClient(universalConn)
	fennelFeatureStoreClient := fennelvgpb.NewFennelFeatureStoreClient(vendorgatewayConn)
	scienapticClient := vgscienapticpb.NewScienapticClient(vendorgatewayConn)
	lendingConn := epifigrpc.NewServerConn(cfg.VENDOR_NOTIFI_SERVER, cfg.LENDING_SERVER)
	defer epifigrpc.CloseConn(lendingConn)
	creditReportManagerClient := creditreport.NewCreditReportManagerClient(lendingConn)
	panClient := panpb.NewPanClient(onboardingConn)
	celestialClient := celestialpb.NewCelestialClient(universalConn)
	vendormappingConn := epifigrpc.NewServerConn(cfg.VENDOR_NOTIFI_SERVER, cfg.VENDORMAPPING_SERVER)
	defer epifigrpc.CloseConn(vendormappingConn)
	vendorMappingServiceClient := vendormappingpb.NewVendorMappingServiceClient(vendormappingConn)
	externalVendorRedemptionServiceClient := evrpb.NewExternalVendorRedemptionServiceClient(universalConn)
	fireflyClient := fireflypb.NewFireflyClient(lendingConn)
	questRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["QuestRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = questRedisStore.Close() }()
	questCacheStorage := cache.NewRedisCacheStorageWithHystrix(cache.NewRedisCacheStorage(questRedisStore), gconf.RedisClusters()["QuestRedisStore"].HystrixCommand)
	rewardsGeneratorClient := rewardspb.NewRewardsGeneratorClient(universalConn)
	txnAggregatesClient := txnaggregatespb.NewTxnAggregatesClient(universalConn)
	savingsClient := savingspb.NewSavingsClient(universalConn)
	merchantServiceClient := merchantpb.NewMerchantServiceClient(payConn)
	piClient := paymentinstrumentpb.NewPiClient(payConn)
	txnCategorizerClient := categorizerpb.NewTxnCategorizerClient(universalConn)
	preApprovedLoanClient := preapprovedloan.NewPreApprovedLoanClient(lendingConn)
	balanceClient := balancepb.NewBalanceClient(payConn)
	creditCardClient := creditcardvgpb.NewCreditCardClient(vendorgatewayConn)
	accountingClient := ffaccpb.NewAccountingClient(lendingConn)
	billingClient := ffbillingpb.NewBillingClient(lendingConn)
	depositClient := depositpb.NewDepositClient(universalConn)
	cardProvisioningClient := provisioning.NewCardProvisioningClient(payConn)
	payClient := pay.NewPayClient(payConn)
	uSStocksRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["USStocksRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = uSStocksRedisStore.Close() }()
	catalogManagerClient := usstockscatalogpb.NewCatalogManagerClient(universalConn)
	salaryProgramClient := salarypb.NewSalaryProgramClient(universalConn)
	ticketClient := ticketpb.NewTicketClient(universalConn)
	upiOnboardingClient := upionbpb.NewUpiOnboardingClient(payConn)
	accountPIRelationClient := accountpipb.NewAccountPIRelationClient(payConn)
	netWorthClient := networthpb.NewNetWorthClient(universalConn)
	catalogManagerClientVar2 := catalogpb.NewCatalogManagerClient(universalConn)
	serviceClient := userdeclarationpb.NewServiceClient(universalConn)
	investmentAnalyticsClient := investment.NewInvestmentAnalyticsClient(universalConn)
	epfClient := beepfpb.NewEpfClient(universalConn)
	variableGeneratorClient := variablespb.NewVariableGeneratorClient(universalConn)
	sprinklrClient := sprinklrpb.NewSprinklrClient(universalConn)
	collectionClient := collection.NewCollectionClient(lendingConn)
	sMSClient := vgsmspb.NewSMSClient(vendorgatewayConn)
	analyticsClient := aaanalyticspb.NewAnalyticsClient(universalConn)
	federalClient := federalpb.NewFederalClient(universalConn)
	fireflyV2Client := fireflyv2pb.NewFireflyV2Client(lendingConn)
	onboardingClient := onbpb.NewOnboardingClient(onboardingConn)
	userLeadSvcClient := leadspb.NewUserLeadSvcClient(lendingConn)
	serviceClientVar2 := issuereporting.NewServiceClient(universalConn)
	commsClient := commspb.NewCommsClient(universalConn)
	recurringPaymentServiceClient := recurringpay.NewRecurringPaymentServiceClient(payConn)
	tspUserServiceClient := tspuserpb.NewTspUserServiceClient(onboardingConn)
	locationClient := userlocationpb.NewLocationClient(onboardingConn)
	locationClientVar2 := authlocationpb.NewLocationClient(onboardingConn)
	vkycCallClient := vgvkyccallpb.NewVkycCallClient(vendorgatewayConn)
	livenessClient := vglivenesspb.NewLivenessClient(vendorgatewayConn)
	oCRClient := vgocrpb.NewOCRClient(vendorgatewayConn)
	locationClientVar3 := vglocationpb.NewLocationClient(vendorgatewayConn)
	obfuscatorClient := obfuscator.NewObfuscatorClient(onboardingConn)
	consentClient := consent.NewConsentClient(onboardingConn)
	uNNameCheckClient := vgnamecheckpb.NewUNNameCheckClient(vendorgatewayConn)
	amlClient := amlpb.NewAmlClient(universalConn)

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptor := servergenwire.AddQuestUserContextUnaryInterceptor(gconf)
	if unaryServerInterceptor != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptor)
	}

	unaryServerInterceptorVar2 := servergenwire.NewJwtUnaryServerInterceptor(gconf)
	if unaryServerInterceptorVar2 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar2)
	}

	unaryServerInterceptorVar3 := servergenwire.VNRateLimitServerInterceptor(gconf, rateLimiter, attributeRateLimiter)
	if unaryServerInterceptorVar3 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar3)
	}

	var streamInterceptors []grpc.StreamServerInterceptor

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	httpMux := http.NewServeMux()
	_ = httpMux

	err = setupVendornotification(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, orderServiceClient, connectedAccountClient, accountAggregatorClient, callRoutingClient, ivrClient, authClient, rateLimiterRedisStore, liveChatFallbackClient, workflowClient, healthInsuranceClient, disputeClient, fennelFeatureStoreClient, scienapticClient, creditReportManagerClient, panClient, celestialClient, vendorMappingServiceClient, externalVendorRedemptionServiceClient, fireflyClient, questCacheStorage, rewardsGeneratorClient, txnAggregatesClient, savingsClient, merchantServiceClient, piClient, txnCategorizerClient, preApprovedLoanClient, balanceClient, creditCardClient, accountingClient, billingClient, depositClient, cardProvisioningClient, payClient, uSStocksRedisStore, catalogManagerClient, salaryProgramClient, ticketClient, upiOnboardingClient, accountPIRelationClient, netWorthClient, catalogManagerClientVar2, serviceClient, investmentAnalyticsClient, epfClient, variableGeneratorClient, sprinklrClient, collectionClient, sMSClient, analyticsClient, federalClient, fireflyV2Client, onboardingClient, userLeadSvcClient, serviceClientVar2)
	if err != nil {
		return err
	}
	err = setupTsp(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, commsClient, authClient, recurringPaymentServiceClient, tspUserServiceClient, actorClient, locationClient, locationClientVar2, vkycCallClient, livenessClient, oCRClient, locationClientVar3, obfuscatorClient, orderServiceClient, payClient, piClient, consentClient, onboardingClient, uNNameCheckClient, amlClient)
	if err != nil {
		return err
	}
	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	cleanupFn, err := servergenhook.InitVendorNotificationServer(s) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitVendorNotificationServer"), zap.Error(err))
		return err
	}
	defer cleanupFn()

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	initNotifier <- gconf.Name()

	// TODO(Sundeep): Remove this additional logic for starting the servers inside go-routine, by exposing functions
	// from epifiserver package that take errgroup.Group as parameter.
	grp, _ := errgroup.WithContext(context.Background())

	grp.Go(func() error {
		epifiserver.StartSecureServer(s, gconf.ServerPorts(), string(gconf.Name()))
		return nil
	})

	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// nolint: funlen
func setupVendornotification(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	orderServiceClient orderpb.OrderServiceClient,
	connectedAccountClient capb.ConnectedAccountClient,
	accountAggregatorClient vgaapb.AccountAggregatorClient,
	callRoutingClient callroutingpb.CallRoutingClient,
	ivrClient callivrpb.IvrClient,
	authClient authpb.AuthClient,
	rateLimiterRedisStore types.RateLimiterRedisStore,
	liveChatFallbackClient cxlivechatfbkpb.LiveChatFallbackClient,
	workflowClient cxchatbotworkflowpb.WorkflowClient,
	healthInsuranceClient healthinsurancepb.HealthInsuranceClient,
	disputeClient dpb.DisputeClient,
	fennelFeatureStoreClient fennelvgpb.FennelFeatureStoreClient,
	scienapticClient vgscienapticpb.ScienapticClient,
	creditReportManagerClient creditreport.CreditReportManagerClient,
	panClient panpb.PanClient,
	celestialClient celestialpb.CelestialClient,
	vendorMappingServiceClient vendormappingpb.VendorMappingServiceClient,
	externalVendorRedemptionServiceClient evrpb.ExternalVendorRedemptionServiceClient,
	fireflyClient fireflypb.FireflyClient,
	questCacheStorage types.QuestCacheStorage,
	rewardsGeneratorClient rewardspb.RewardsGeneratorClient,
	txnAggregatesClient txnaggregatespb.TxnAggregatesClient,
	savingsClient savingspb.SavingsClient,
	merchantServiceClient merchantpb.MerchantServiceClient,
	piClient paymentinstrumentpb.PiClient,
	txnCategorizerClient categorizerpb.TxnCategorizerClient,
	preApprovedLoanClient preapprovedloan.PreApprovedLoanClient,
	balanceClient balancepb.BalanceClient,
	creditCardClient creditcardvgpb.CreditCardClient,
	accountingClient ffaccpb.AccountingClient,
	billingClient ffbillingpb.BillingClient,
	depositClient depositpb.DepositClient,
	cardProvisioningClient provisioning.CardProvisioningClient,
	payClient pay.PayClient,
	uSStocksRedisStore types.USStocksRedisStore,
	catalogManagerClient usstockscatalogpb.CatalogManagerClient,
	salaryProgramClient salarypb.SalaryProgramClient,
	ticketClient ticketpb.TicketClient,
	upiOnboardingClient upionbpb.UpiOnboardingClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	netWorthClient networthpb.NetWorthClient,
	catalogManagerClientVar2 catalogpb.CatalogManagerClient,
	serviceClient userdeclarationpb.ServiceClient,
	investmentAnalyticsClient investment.InvestmentAnalyticsClient,
	epfClient beepfpb.EpfClient,
	variableGeneratorClient variablespb.VariableGeneratorClient,
	sprinklrClient sprinklrpb.SprinklrClient,
	collectionClient collection.CollectionClient,
	sMSClient vgsmspb.SMSClient,
	analyticsClient aaanalyticspb.AnalyticsClient,
	federalClient federalpb.FederalClient,
	fireflyV2Client fireflyv2pb.FireflyV2Client,
	onboardingClient onbpb.OnboardingClient,
	userLeadSvcClient leadspb.UserLeadSvcClient,
	serviceClientVar2 issuereporting.ServiceClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	vendornotificationConf, err := vendornotificationconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.VENDOR_NOTIFI_SERVICE))
		return err
	}
	_ = vendornotificationConf

	vendornotificationGenConf, err := dynconf.LoadConfigWithQuestConfig(vendornotificationconf.Load, genconf2.NewConfigWithQuest, cfg.VENDOR_NOTIFI_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.VENDOR_NOTIFI_SERVICE))
		return err
	}

	if !conf.QuestSdk.Disable {
		vendornotificationGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: vendornotificationGenConf, SdkConfig: vendornotificationGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{vendornotificationGenConfAppConfig}, string(cfg.VENDOR_NOTIFI_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = vendornotificationGenConf

	updateTransactionEventsPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.UpdateTransactionEventsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	inboundTxnPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.InboundTxnPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	uPIReqAuthEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.UPIReqAuthEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	uPIRespPayEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.UPIRespPayEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	uPIRespMandateEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.UPIRespMandateEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	uPIReqTxnConfirmationEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.UPIReqTxnConfirmationEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	uPIReqValAddressEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.UPIReqValAddressEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	uPIListPspKeysEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.UPIListPspKeysEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	uPIReqAuthMandateEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.UPIReqAuthMandateEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	uPIReqMandateConfirmationEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.UPIReqMandateConfirmationEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	uPIReqTxnConfirmationComplaintEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.UPIReqTxnConfirmationComplaintEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	uPIReqAuthValCustEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.UPIReqAuthValCustEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	inboundUpiTxnPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.InboundUpiTxnPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	inboundLoanTxnPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.InboundLoanTxnPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	uPIRespComplaintEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.UPIRespComplaintEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	uPIReqMapperConfirmationEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.UPIReqMapperConfirmationEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	createCardCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.CreateCardCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	dispatchPhysicalCardCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.DispatchPhysicalCardCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	cardSwitchFinancialNotificationPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.CardSwitchFinancialNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	cardSwitchNonFinancialNotificationPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.CardSwitchNonFinancialNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	aclSmsCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.AclSmsCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	kaleyraSmsCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.KaleyraSmsCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	aclWhatsappCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.AclWhatsappCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	aclWhatsappReplyPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.AclWhatsappReplyPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	karzaVkycCallEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.KarzaVkycCallEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	karzaVkycAgentResponsePublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.KarzaVkycAgentResponsePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	karzaVkycAuditorResponsePublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.KarzaVkycAuditorResponsePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	updateShippingAddressCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.UpdateShippingAddressCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	createDepositCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.CreateDepositCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	preCloseDepositCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.PreCloseDepositCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	fdAutoRenewCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.FdAutoRenewCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	federalVkycUpdatePublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.FederalVkycUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	federalBankCustKycStateChangePublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.FederalBankCustKycStateChangePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	bankCustCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.BankCustCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	accountCreationCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.AccountCreationCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	accountStatusCallBackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.AccountStatusCallBackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	federalResidentialStatusUpdatePublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.FederalResidentialStatusUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	deviceReRegCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.DeviceReRegCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	deviceRegSMSAckPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.DeviceRegSMSAckPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	federalMobileNumberUpdatePublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.FederalMobileNumberUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	emailCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.EmailCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	consentCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.ConsentCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	fICallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.FICallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	accountLinkStatusCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.AccountLinkStatusCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	cardTrackingCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.CardTrackingCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	ozonetelCallDetailsPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.OzonetelCallDetailsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	freshchatActionCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.FreshchatActionCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	cCTransactionNotificationPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.CCTransactionNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	cCStatementNotificationPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.CCStatementNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	cCAcsNotificationPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.CCAcsNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	cCNonFinancialNotificationPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.CCNonFinancialNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	tssWebhookCallBackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.TssWebhookCallBackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	healthInsurancePolicyIssuanceEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.HealthInsurancePolicyIssuanceEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	signalWorkflowPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.SignalWorkflowPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	kycStatusUpdatePublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.KycStatusUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	enachRegistrationAuthorisationCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.EnachRegistrationAuthorisationCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	gupshupWhatsappCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.GupshupWhatsappCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	gupshupRcsCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.GupshupRcsCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	netCoreSmsCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.NetCoreSmsCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	pgRazorpayInboundEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.PgRazorpayInboundEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	airtelSmsCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.AirtelSmsCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	airtelWhatsappCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.AirtelWhatsappCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	federalEscalationUpdateEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.FederalEscalationUpdateEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	ccOnboardingStateUpdateEventPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.CcOnboardingStateUpdateEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	vendorRewardFulfillmentPublisher, err := sqs.NewPublisherWithConfig(ctx, vendornotificationGenConf.VendorRewardFulfillmentPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	ccSwitchNotificationsS3Client := s3pkg.NewClient(awsConf, vendornotificationGenConf.CcSwitchNotificationsBucketName())
	ccRawSwitchNotificationsS3Client := s3pkg.NewClient(awsConf, vendornotificationGenConf.CcRawSwitchNotificationsBucketName())
	epanCallbackS3Client := s3pkg.NewClient(awsConf, vendornotificationGenConf.EpanCallbackBucketName())

	service, err := wire.InitializeFederalPayService(ctx, vendornotificationConf, awsConf, updateTransactionEventsPublisher, inboundTxnPublisher, uPIReqAuthEventPublisher, uPIRespPayEventPublisher, uPIRespMandateEventPublisher, uPIReqTxnConfirmationEventPublisher, uPIReqValAddressEventPublisher, uPIListPspKeysEventPublisher, orderServiceClient, uPIReqAuthMandateEventPublisher, uPIReqMandateConfirmationEventPublisher, uPIReqTxnConfirmationComplaintEventPublisher, uPIReqAuthValCustEventPublisher, inboundUpiTxnPublisher, inboundLoanTxnPublisher, uPIRespComplaintEventPublisher, uPIReqMapperConfirmationEventPublisher, vendornotificationGenConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	federalpaymentpb.RegisterPaymentServer(s, service)

	serviceVar2 := wire.InitializeFederalCardService(createCardCallbackPublisher, dispatchPhysicalCardCallbackPublisher, cardSwitchFinancialNotificationPublisher, cardSwitchNonFinancialNotificationPublisher)

	federalcardpb.RegisterCardServer(s, serviceVar2)

	serviceVar3 := wire.InitializeKarzaLivenessService()

	karza2.RegisterLivenessServer(s, serviceVar3)

	serviceVar4 := wire.InitializeSmsAclCallbackService(aclSmsCallbackPublisher, vendornotificationConf)

	aclpb.RegisterSmsCallbackServer(s, serviceVar4)

	serviceVar5 := wire.InitializeKaleyraCallbackService(kaleyraSmsCallbackPublisher, vendornotificationConf)

	kaleyrapb.RegisterKaleyraCallbackServer(s, serviceVar5)

	serviceVar6 := wire.InitializeWhatsappAclCallbackService(vendornotificationConf, aclWhatsappCallbackPublisher, aclWhatsappReplyPublisher)

	aclwapb.RegisterWhatsappCallbackServer(s, serviceVar6)

	serviceVar7 := wire.InitializeVKYCKarzaCallbackService(karzaVkycCallEventPublisher, karzaVkycAgentResponsePublisher, karzaVkycAuditorResponsePublisher, vendornotificationConf)

	karza4.RegisterVKYCKarzaServer(s, serviceVar7)

	serviceVar8 := wire.InitializeShippingAddressUpdateCallbackService(updateShippingAddressCallbackPublisher)

	federalshippingaddressupdatepb.RegisterShippingPreferenceServer(s, serviceVar8)

	serviceVar9 := wire.InitializeFederalDepositService(createDepositCallbackPublisher, preCloseDepositCallbackPublisher, fdAutoRenewCallbackPublisher)

	federaldepositpb.RegisterDepositServer(s, serviceVar9)

	serviceVar10 := wire.InitializeKycTypeChangeCallbackService(federalVkycUpdatePublisher, federalBankCustKycStateChangePublisher)

	federalkyctypechangepb.RegisterKycTypeChangeServer(s, serviceVar10)

	serviceVar11 := wire.InitializeAccountsCallbackService(bankCustCallbackPublisher, accountCreationCallbackPublisher, vendornotificationGenConf, accountStatusCallBackPublisher, federalResidentialStatusUpdatePublisher)

	federalaccountspb.RegisterAccountsServer(s, serviceVar11)

	serviceVar12 := wire.InitializeFederalAuthService(deviceReRegCallbackPublisher, deviceRegSMSAckPublisher, federalMobileNumberUpdatePublisher)

	federalauthpb.RegisterAuthServer(s, serviceVar12)

	serviceVar13 := wire.InitializeEmailCallbackService(emailCallbackPublisher, vendornotificationConf)

	email2.RegisterEmailCallbackServer(s, serviceVar13)

	notificationService := wire.InitializeAANotificationService(consentCallbackPublisher, fICallbackPublisher, connectedAccountClient, vendornotificationGenConf, accountAggregatorClient, accountLinkStatusCallbackPublisher)

	aa2.RegisterAANotificationServer(s, notificationService)

	serviceVar14 := wire.InitialiseShipwayCardService(cardTrackingCallbackPublisher)

	shipwaycardpb.RegisterShipwayServer(s, serviceVar14)

	callRoutingService := wire.InitializeOzonetelCallRoutingService(callRoutingClient, vendornotificationConf, ozonetelCallDetailsPublisher, ivrClient)

	ozonetel2.RegisterCallRoutingServer(s, callRoutingService)

	serviceVar15 := wire.InitializeAxisAccountsCallBackService(vendornotificationConf)

	axisaccounts2.RegisterAccountsServer(s, serviceVar15)

	serviceVar16 := wire.InitializeAxisPaymentCallBackService(vendornotificationConf)

	axispayment2.RegisterPaymentServer(s, serviceVar16)

	serviceVar17 := wire.InitializeAxisEKycCallBackService(vendornotificationConf)

	axisekyc2.RegisterEkycServer(s, serviceVar17)

	serviceVar18, err := wire.InitializeSenseforthChatBotAuthService(vendornotificationConf, authClient, rateLimiterRedisStore)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	chatbotauthpb.RegisterChatBotAuthServer(s, serviceVar18)

	serviceVar19, err := wire.InitializeSenseforthLiveChatFallbackService(vendornotificationConf, liveChatFallbackClient, authClient, rateLimiterRedisStore)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	senseforth2.RegisterLiveChatFallbackServer(s, serviceVar19)

	serviceVar20 := wire.InitializeFreshchatCallbackService(vendornotificationConf, freshchatActionCallbackPublisher)

	freshchat2.RegisterFreshchatCallbackServer(s, serviceVar20)

	serviceVar21, err := wire.InitializeM2PCreditCardService(ctx, cCTransactionNotificationPublisher, cCStatementNotificationPublisher, cCAcsNotificationPublisher, vendornotificationGenConf, awsConf, vendornotificationConf, ccSwitchNotificationsS3Client, ccRawSwitchNotificationsS3Client, cCNonFinancialNotificationPublisher)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	ccpb.RegisterCreditCardServer(s, serviceVar21)

	serviceVar22 := wire.InitializeChatbotWorkflowService(vendornotificationConf, authClient, workflowClient)

	vnchatbotworkflowpb.RegisterChatBotWorkflowServer(s, serviceVar22)

	serviceVar23 := wire.InitializeTssWebhookCallBackService(vendornotificationConf, tssWebhookCallBackPublisher)

	tss2.RegisterTSSServer(s, serviceVar23)

	serviceVar24 := wire.InitializeRiskcovryCallbackService(vendornotificationConf, healthInsuranceClient, healthInsurancePolicyIssuanceEventPublisher)

	riskcovryvnpb.RegisterRiskcovryCallbackServer(s, serviceVar24)

	serviceVar25 := wire.InitializeSmallcaseWebhookService(ctx, vendornotificationConf, awsConf)

	smallcase2.RegisterSmallcaseServer(s, serviceVar25)

	serviceVar26 := wire.InitializeFederalDmpService(disputeClient)

	federaldmppb.RegisterDmpServer(s, serviceVar26)

	serviceVar27 := wire.InitializeInhouseBreService(fennelFeatureStoreClient, scienapticClient, usersClient, vendornotificationConf, creditReportManagerClient)

	inhousebrevnpb.RegisterBreServer(s, serviceVar27)

	serviceVar28 := wire.InitializeEPANCallbackService(vendornotificationConf, signalWorkflowPublisher, panClient, celestialClient, epanCallbackS3Client)

	epanvnpb.RegisterEPANServer(s, serviceVar28)

	serviceVar29 := wire.InitializeFiCoinsAccountingService(vendornotificationGenConf, vendorMappingServiceClient, externalVendorRedemptionServiceClient, usersClient, fireflyClient)

	ficoinsaccvnpb2.RegisterFiCoinsAccountingServer(s, serviceVar29)

	serviceVar30 := wire.InitializeFiftyfinNotificationService(vendornotificationConf, celestialClient)

	fiftyfinvnpb.RegisterFiftyFinCallbackServer(s, serviceVar30)

	serviceVar31 := wire.InitializeIdfcKycCallbackService(kycStatusUpdatePublisher)

	vnidfcpb.RegisterIdfcServer(s, serviceVar31)

	serviceVar32 := wire.InitializeLendingFederalInboundService(vendornotificationConf)

	lendingfederalpb.RegisterLoansServer(s, serviceVar32)

	serviceVar33 := wire.InitializeMoengageService(vendornotificationConf, vendornotificationGenConf, questCacheStorage, vendorMappingServiceClient, rewardsGeneratorClient, actorClient, usersClient, groupClient, segmentationServiceClient, managerClient, broker, txnAggregatesClient, savingsClient, fireflyClient, connectedAccountClient, merchantServiceClient, piClient, txnCategorizerClient, preApprovedLoanClient, balanceClient, creditCardClient, accountingClient, billingClient, depositClient, cardProvisioningClient, payClient, uSStocksRedisStore, catalogManagerClient, salaryProgramClient, ticketClient, upiOnboardingClient, accountPIRelationClient, netWorthClient, catalogManagerClientVar2, serviceClient, investmentAnalyticsClient, epfClient, variableGeneratorClient)

	moengagevnpb.RegisterMoengageServer(s, serviceVar33)

	serviceVar34 := wire.InitialiseRecurringPaymentFederalService(enachRegistrationAuthorisationCallbackPublisher)

	rpfederalvnpb.RegisterRecurringPaymentServer(s, serviceVar34)

	eventsHandlingService := wire.InitializeSprinklrEventHandlingService(sprinklrClient, vendornotificationConf)

	sprinklrpb2.RegisterSprinklrEventHandlingServer(s, eventsHandlingService)

	serviceVar35 := wire.InitializeAbflNotificationService(vendornotificationConf, celestialClient)

	abfl.RegisterAbflCallbackServer(s, serviceVar35)

	serviceVar36 := wire.InitializeSetuNotificationService(vendornotificationConf, celestialClient)

	setu.RegisterSetuCallbackServer(s, serviceVar36)

	serviceVar37 := wire.InitializeMoneyviewCallbackService(vendornotificationConf, celestialClient)

	moneyview.RegisterMoneyviewCallbackServer(s, serviceVar37)

	serviceVar38 := wire.InitializeExternalOfferRedemptionsService(vendornotificationGenConf, vendorMappingServiceClient, externalVendorRedemptionServiceClient, usersClient)

	offersvnpb2.RegisterExternalRedemptionsServer(s, serviceVar38)

	serviceVar39 := wire.InitializeCommsUnsubscribeService(vendornotificationGenConf, vendorMappingServiceClient, usersClient)

	unsubscribe.RegisterUnsubscribeServer(s, serviceVar39)

	serviceVar40 := wire.InitializeCredgenicsWebhookService(vendornotificationConf, awsConf, preApprovedLoanClient, collectionClient)

	credgenicsvnpb.RegisterCredgenicsWebhookServer(s, serviceVar40)

	serviceVar41 := wire.InitializeWhatsappGupshupCallbackService(vendornotificationConf, gupshupWhatsappCallbackPublisher)

	gupshupvnpb.RegisterGupshupCallbackServer(s, serviceVar41)

	serviceVar42 := wire.InitializePaisabazaarCallBackService(fireflyClient)

	paisabazaar2.RegisterPaisabazaarCallbackServer(s, serviceVar42)

	serviceVar43 := wire.InitializeRcsGupshupCallbackService(vendornotificationConf, gupshupRcsCallbackPublisher)

	gupshuprcsvnpb.RegisterGupshupRcsCallbackServer(s, serviceVar43)

	serviceVar44 := wire.InitializeVideoSdkService()

	videosdk2.RegisterVideoSdkCallbackServer(s, serviceVar44)

	serviceVar45 := wire.InitializeNetCoreCallbackService(netCoreSmsCallbackPublisher, vendornotificationConf)

	netcorevnpb.RegisterNetCoreCallbackServer(s, serviceVar45)

	serviceVar46 := wire.InitializeRazorpayInboundEventService(vendornotificationGenConf, pgRazorpayInboundEventPublisher)

	razorpayvnpb.RegisterRazorpayPaymentGatewayServer(s, serviceVar46)

	serviceVar47 := wire.InitializeAirtelCallbackService(vendornotificationConf, airtelSmsCallbackPublisher, sMSClient)

	airtelvnpb.RegisterAirtelSMSCallbackServer(s, serviceVar47)

	serviceVar48 := wire.InitializeAirtelWhatsappCallbackService(vendornotificationConf, airtelWhatsappCallbackPublisher)

	airtelwpvnpb.RegisterAirtelCallbackServer(s, serviceVar48)

	ignosisAaService := wire.InitializeIgnosisAaService(vendornotificationGenConf, analyticsClient)

	igvnpb.RegisterIgnosisAaAnalyticsNotificationServer(s, ignosisAaService)

	federalEventsHandlingService := wire.InitializeFederalEventHandlingService(federalClient, vendornotificationGenConf, federalEscalationUpdateEventPublisher)

	cxescalationpb.RegisterFederalEscalationHandlingServer(s, federalEventsHandlingService)

	serviceVar49 := wire.InitializeSavenCallBackService(fireflyV2Client, vendornotificationConf, ccOnboardingStateUpdateEventPublisher)

	savenvnpb.RegisterSavenCallbackServer(s, serviceVar49)

	serviceVar50 := wire.InitializeAuthService(vendornotificationConf)

	authpb2.RegisterAuthServer(s, serviceVar50)

	serviceVar51, err := wire.InitializeRewardService(vendornotificationGenConf, vendornotificationConf, fireflyV2Client, rewardsGeneratorClient, vendorRewardFulfillmentPublisher)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	rewardpb.RegisterRewardServer(s, serviceVar51)

	serviceVar52 := wire2.InitialiseExternalLeadsService(onboardingClient, userLeadSvcClient, vendornotificationConf)

	leadsexternalpb.RegisterLeadServer(s, serviceVar52)

	serviceVar53 := wire.InitializeFreshchatAIBotService(vendornotificationGenConf, usersClient, serviceClientVar2, vendorMappingServiceClient)

	freshchat3.RegisterFreshchatAIBotServiceServer(s, serviceVar53)

	nuggetService := wire.InitializeNuggetService(vendornotificationConf)

	nugget2.RegisterChatbotNuggetServiceServer(s, nuggetService)

	configNameToConfMap[cfg.ConfigName(cfg.VENDOR_NOTIFI_SERVICE)] = &commonexplorer.Config{StaticConf: &vendornotificationconf.Config{}, QuestIntegratedConfig: vendornotificationGenConf}

	return nil

}

// nolint: funlen
func setupTsp(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	commsClient commspb.CommsClient,
	authClient authpb.AuthClient,
	recurringPaymentServiceClient recurringpay.RecurringPaymentServiceClient,
	tspUserServiceClient tspuserpb.TspUserServiceClient,
	actorClient actor.ActorClient,
	locationClient userlocationpb.LocationClient,
	locationClientVar2 authlocationpb.LocationClient,
	vkycCallClient vgvkyccallpb.VkycCallClient,
	livenessClient vglivenesspb.LivenessClient,
	oCRClient vgocrpb.OCRClient,
	locationClientVar3 vglocationpb.LocationClient,
	obfuscatorClient obfuscator.ObfuscatorClient,
	orderServiceClient orderpb.OrderServiceClient,
	payClient pay.PayClient,
	piClient paymentinstrumentpb.PiClient,
	consentClient consent.ConsentClient,
	onboardingClient onbpb.OnboardingClient,
	uNNameCheckClient vgnamecheckpb.UNNameCheckClient,
	amlClient amlpb.AmlClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	tspConf, err := tspconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.TSP_SERVICE))
		return err
	}
	_ = tspConf

	serviceVar54 := wire3.InitialiseCommsService(commsClient)

	tspcommspb.RegisterCommsServer(s, serviceVar54)

	serviceVar55 := wire3.InitialiseAuthService(authClient)

	tspauthpb.RegisterAuthServer(s, serviceVar55)

	serviceVar56 := wire3.InitialiseMandateService(recurringPaymentServiceClient, tspUserServiceClient, actorClient)

	mandate2.RegisterMandateServer(s, serviceVar56)

	serviceVar57 := wire3.InitialiseUserLocationService(locationClient)

	userlocationpb.RegisterLocationServer(s, serviceVar57)

	serviceVar58 := wire3.InitialiseAuthLocationService(locationClientVar2)

	authlocationpb.RegisterLocationServer(s, serviceVar58)

	serviceVar59 := wire3.InitialiseVgVkycCallService(vkycCallClient)

	vgvkyccallpb.RegisterVkycCallServer(s, serviceVar59)

	serviceVar60 := wire3.InitialiseVgLivenessService(livenessClient)

	vglivenesspb.RegisterLivenessServer(s, serviceVar60)

	serviceVar61 := wire3.InitialiseVgOcrService(oCRClient)

	vgocrpb.RegisterOCRServer(s, serviceVar61)

	serviceVar62 := wire3.InitialiseVgLocationService(locationClientVar3)

	vglocationpb.RegisterLocationServer(s, serviceVar62)

	serviceVar63 := wire3.InitialiseObfuscatorService(obfuscatorClient)

	obfuscator.RegisterObfuscatorServer(s, serviceVar63)

	serviceVar64 := wire3.InitialisePaymentService(orderServiceClient, payClient, tspUserServiceClient, actorClient)

	payment2.RegisterPaymentServer(s, serviceVar64)

	serviceVar65 := wire3.InitialiseUserService(tspUserServiceClient, actorClient)

	tspuserpb2.RegisterUserServer(s, serviceVar65)

	serviceVar66 := wire3.InitialisePennyDropService(payClient, piClient, tspUserServiceClient, actorClient)

	pennydrop2.RegisterPennyDropServer(s, serviceVar66)

	serviceVar67 := wire3.InitialisePaymentInstrumentService(piClient)

	paymentinstrument2.RegisterPaymentInstrumentServiceServer(s, serviceVar67)

	serviceVar68 := wire3.InitialiseConsentService(consentClient, tspUserServiceClient, actorClient)

	consent3.RegisterConsentServer(s, serviceVar68)

	serviceVar69 := wire3.InitialisePersistentQueueService(onboardingClient)

	tsppqueuepb.RegisterPersistentQueueServer(s, serviceVar69)

	serviceVar70 := wire3.InitialiseNameCheckService(uNNameCheckClient)

	vgnamecheckpb.RegisterUNNameCheckServer(s, serviceVar70)

	serviceVar71 := wire3.InitialiseAmlService(tspUserServiceClient, actorClient, amlClient)

	tspamlpb.RegisterAmlServer(s, serviceVar71)

	serviceVar72 := wire3.InitializeOnboardingService(onboardingClient)

	onbpb.RegisterOnboardingServer(s, serviceVar72)

	configNameToConfMap[cfg.ConfigName(cfg.TSP_SERVICE)] = &commonexplorer.Config{StaticConf: &tspconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.VENDOR_NOTIFI_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
