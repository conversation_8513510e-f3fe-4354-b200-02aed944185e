// Code generated by MockGen. DO NOT EDIT.
// Source: api/webfe/accounts/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	accounts "github.com/epifi/gamma/api/webfe/accounts"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAccountsClient is a mock of AccountsClient interface.
type MockAccountsClient struct {
	ctrl     *gomock.Controller
	recorder *MockAccountsClientMockRecorder
}

// MockAccountsClientMockRecorder is the mock recorder for MockAccountsClient.
type MockAccountsClientMockRecorder struct {
	mock *MockAccountsClient
}

// NewMockAccountsClient creates a new mock instance.
func NewMockAccountsClient(ctrl *gomock.Controller) *MockAccountsClient {
	mock := &MockAccountsClient{ctrl: ctrl}
	mock.recorder = &MockAccountsClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountsClient) EXPECT() *MockAccountsClientMockRecorder {
	return m.recorder
}

// ShareAlternateAccountForBalanceTransfer mocks base method.
func (m *MockAccountsClient) ShareAlternateAccountForBalanceTransfer(ctx context.Context, in *accounts.ShareAlternateAccountForBalanceTransferRequest, opts ...grpc.CallOption) (*accounts.ShareAlternateAccountForBalanceTransferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ShareAlternateAccountForBalanceTransfer", varargs...)
	ret0, _ := ret[0].(*accounts.ShareAlternateAccountForBalanceTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShareAlternateAccountForBalanceTransfer indicates an expected call of ShareAlternateAccountForBalanceTransfer.
func (mr *MockAccountsClientMockRecorder) ShareAlternateAccountForBalanceTransfer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShareAlternateAccountForBalanceTransfer", reflect.TypeOf((*MockAccountsClient)(nil).ShareAlternateAccountForBalanceTransfer), varargs...)
}

// VerifyPan mocks base method.
func (m *MockAccountsClient) VerifyPan(ctx context.Context, in *accounts.VerifyPanRequest, opts ...grpc.CallOption) (*accounts.VerifyPanResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyPan", varargs...)
	ret0, _ := ret[0].(*accounts.VerifyPanResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyPan indicates an expected call of VerifyPan.
func (mr *MockAccountsClientMockRecorder) VerifyPan(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyPan", reflect.TypeOf((*MockAccountsClient)(nil).VerifyPan), varargs...)
}

// MockAccountsServer is a mock of AccountsServer interface.
type MockAccountsServer struct {
	ctrl     *gomock.Controller
	recorder *MockAccountsServerMockRecorder
}

// MockAccountsServerMockRecorder is the mock recorder for MockAccountsServer.
type MockAccountsServerMockRecorder struct {
	mock *MockAccountsServer
}

// NewMockAccountsServer creates a new mock instance.
func NewMockAccountsServer(ctrl *gomock.Controller) *MockAccountsServer {
	mock := &MockAccountsServer{ctrl: ctrl}
	mock.recorder = &MockAccountsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountsServer) EXPECT() *MockAccountsServerMockRecorder {
	return m.recorder
}

// ShareAlternateAccountForBalanceTransfer mocks base method.
func (m *MockAccountsServer) ShareAlternateAccountForBalanceTransfer(arg0 context.Context, arg1 *accounts.ShareAlternateAccountForBalanceTransferRequest) (*accounts.ShareAlternateAccountForBalanceTransferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShareAlternateAccountForBalanceTransfer", arg0, arg1)
	ret0, _ := ret[0].(*accounts.ShareAlternateAccountForBalanceTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShareAlternateAccountForBalanceTransfer indicates an expected call of ShareAlternateAccountForBalanceTransfer.
func (mr *MockAccountsServerMockRecorder) ShareAlternateAccountForBalanceTransfer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShareAlternateAccountForBalanceTransfer", reflect.TypeOf((*MockAccountsServer)(nil).ShareAlternateAccountForBalanceTransfer), arg0, arg1)
}

// VerifyPan mocks base method.
func (m *MockAccountsServer) VerifyPan(arg0 context.Context, arg1 *accounts.VerifyPanRequest) (*accounts.VerifyPanResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyPan", arg0, arg1)
	ret0, _ := ret[0].(*accounts.VerifyPanResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyPan indicates an expected call of VerifyPan.
func (mr *MockAccountsServerMockRecorder) VerifyPan(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyPan", reflect.TypeOf((*MockAccountsServer)(nil).VerifyPan), arg0, arg1)
}

// MockUnsafeAccountsServer is a mock of UnsafeAccountsServer interface.
type MockUnsafeAccountsServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAccountsServerMockRecorder
}

// MockUnsafeAccountsServerMockRecorder is the mock recorder for MockUnsafeAccountsServer.
type MockUnsafeAccountsServerMockRecorder struct {
	mock *MockUnsafeAccountsServer
}

// NewMockUnsafeAccountsServer creates a new mock instance.
func NewMockUnsafeAccountsServer(ctrl *gomock.Controller) *MockUnsafeAccountsServer {
	mock := &MockUnsafeAccountsServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAccountsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAccountsServer) EXPECT() *MockUnsafeAccountsServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAccountsServer mocks base method.
func (m *MockUnsafeAccountsServer) mustEmbedUnimplementedAccountsServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAccountsServer")
}

// mustEmbedUnimplementedAccountsServer indicates an expected call of mustEmbedUnimplementedAccountsServer.
func (mr *MockUnsafeAccountsServerMockRecorder) mustEmbedUnimplementedAccountsServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAccountsServer", reflect.TypeOf((*MockUnsafeAccountsServer)(nil).mustEmbedUnimplementedAccountsServer))
}
