// Code generated by MockGen. DO NOT EDIT.
// Source: api/webfe/auth/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	auth "github.com/epifi/gamma/api/webfe/auth"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAuthClient is a mock of AuthClient interface.
type MockAuthClient struct {
	ctrl     *gomock.Controller
	recorder *MockAuthClientMockRecorder
}

// MockAuthClientMockRecorder is the mock recorder for MockAuthClient.
type MockAuthClientMockRecorder struct {
	mock *MockAuthClient
}

// NewMockAuthClient creates a new mock instance.
func NewMockAuthClient(ctrl *gomock.Controller) *MockAuthClient {
	mock := &MockAuthClient{ctrl: ctrl}
	mock.recorder = &MockAuthClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAuthClient) EXPECT() *MockAuthClientMockRecorder {
	return m.recorder
}

// GenerateOtp mocks base method.
func (m *MockAuthClient) GenerateOtp(ctx context.Context, in *auth.GenerateOtpRequest, opts ...grpc.CallOption) (*auth.GenerateOtpResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateOtp", varargs...)
	ret0, _ := ret[0].(*auth.GenerateOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateOtp indicates an expected call of GenerateOtp.
func (mr *MockAuthClientMockRecorder) GenerateOtp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateOtp", reflect.TypeOf((*MockAuthClient)(nil).GenerateOtp), varargs...)
}

// VerifyOtp mocks base method.
func (m *MockAuthClient) VerifyOtp(ctx context.Context, in *auth.VerifyOtpRequest, opts ...grpc.CallOption) (*auth.VerifyOtpResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyOtp", varargs...)
	ret0, _ := ret[0].(*auth.VerifyOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyOtp indicates an expected call of VerifyOtp.
func (mr *MockAuthClientMockRecorder) VerifyOtp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyOtp", reflect.TypeOf((*MockAuthClient)(nil).VerifyOtp), varargs...)
}

// MockAuthServer is a mock of AuthServer interface.
type MockAuthServer struct {
	ctrl     *gomock.Controller
	recorder *MockAuthServerMockRecorder
}

// MockAuthServerMockRecorder is the mock recorder for MockAuthServer.
type MockAuthServerMockRecorder struct {
	mock *MockAuthServer
}

// NewMockAuthServer creates a new mock instance.
func NewMockAuthServer(ctrl *gomock.Controller) *MockAuthServer {
	mock := &MockAuthServer{ctrl: ctrl}
	mock.recorder = &MockAuthServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAuthServer) EXPECT() *MockAuthServerMockRecorder {
	return m.recorder
}

// GenerateOtp mocks base method.
func (m *MockAuthServer) GenerateOtp(arg0 context.Context, arg1 *auth.GenerateOtpRequest) (*auth.GenerateOtpResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateOtp", arg0, arg1)
	ret0, _ := ret[0].(*auth.GenerateOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateOtp indicates an expected call of GenerateOtp.
func (mr *MockAuthServerMockRecorder) GenerateOtp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateOtp", reflect.TypeOf((*MockAuthServer)(nil).GenerateOtp), arg0, arg1)
}

// VerifyOtp mocks base method.
func (m *MockAuthServer) VerifyOtp(arg0 context.Context, arg1 *auth.VerifyOtpRequest) (*auth.VerifyOtpResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyOtp", arg0, arg1)
	ret0, _ := ret[0].(*auth.VerifyOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyOtp indicates an expected call of VerifyOtp.
func (mr *MockAuthServerMockRecorder) VerifyOtp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyOtp", reflect.TypeOf((*MockAuthServer)(nil).VerifyOtp), arg0, arg1)
}

// MockUnsafeAuthServer is a mock of UnsafeAuthServer interface.
type MockUnsafeAuthServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAuthServerMockRecorder
}

// MockUnsafeAuthServerMockRecorder is the mock recorder for MockUnsafeAuthServer.
type MockUnsafeAuthServerMockRecorder struct {
	mock *MockUnsafeAuthServer
}

// NewMockUnsafeAuthServer creates a new mock instance.
func NewMockUnsafeAuthServer(ctrl *gomock.Controller) *MockUnsafeAuthServer {
	mock := &MockUnsafeAuthServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAuthServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAuthServer) EXPECT() *MockUnsafeAuthServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAuthServer mocks base method.
func (m *MockUnsafeAuthServer) mustEmbedUnimplementedAuthServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAuthServer")
}

// mustEmbedUnimplementedAuthServer indicates an expected call of mustEmbedUnimplementedAuthServer.
func (mr *MockUnsafeAuthServerMockRecorder) mustEmbedUnimplementedAuthServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAuthServer", reflect.TypeOf((*MockUnsafeAuthServer)(nil).mustEmbedUnimplementedAuthServer))
}
