// Code generated by MockGen. DO NOT EDIT.
// Source: api/webfe/consent/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	consent "github.com/epifi/gamma/api/webfe/consent"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockConsentClient is a mock of ConsentClient interface.
type MockConsentClient struct {
	ctrl     *gomock.Controller
	recorder *MockConsentClientMockRecorder
}

// MockConsentClientMockRecorder is the mock recorder for MockConsentClient.
type MockConsentClientMockRecorder struct {
	mock *MockConsentClient
}

// NewMockConsentClient creates a new mock instance.
func NewMockConsentClient(ctrl *gomock.Controller) *MockConsentClient {
	mock := &MockConsentClient{ctrl: ctrl}
	mock.recorder = &MockConsentClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsentClient) EXPECT() *MockConsentClientMockRecorder {
	return m.recorder
}

// RecordConsents mocks base method.
func (m *MockConsentClient) RecordConsents(ctx context.Context, in *consent.RecordConsentsRequest, opts ...grpc.CallOption) (*consent.RecordConsentsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecordConsents", varargs...)
	ret0, _ := ret[0].(*consent.RecordConsentsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordConsents indicates an expected call of RecordConsents.
func (mr *MockConsentClientMockRecorder) RecordConsents(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordConsents", reflect.TypeOf((*MockConsentClient)(nil).RecordConsents), varargs...)
}

// MockConsentServer is a mock of ConsentServer interface.
type MockConsentServer struct {
	ctrl     *gomock.Controller
	recorder *MockConsentServerMockRecorder
}

// MockConsentServerMockRecorder is the mock recorder for MockConsentServer.
type MockConsentServerMockRecorder struct {
	mock *MockConsentServer
}

// NewMockConsentServer creates a new mock instance.
func NewMockConsentServer(ctrl *gomock.Controller) *MockConsentServer {
	mock := &MockConsentServer{ctrl: ctrl}
	mock.recorder = &MockConsentServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsentServer) EXPECT() *MockConsentServerMockRecorder {
	return m.recorder
}

// RecordConsents mocks base method.
func (m *MockConsentServer) RecordConsents(arg0 context.Context, arg1 *consent.RecordConsentsRequest) (*consent.RecordConsentsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordConsents", arg0, arg1)
	ret0, _ := ret[0].(*consent.RecordConsentsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordConsents indicates an expected call of RecordConsents.
func (mr *MockConsentServerMockRecorder) RecordConsents(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordConsents", reflect.TypeOf((*MockConsentServer)(nil).RecordConsents), arg0, arg1)
}

// MockUnsafeConsentServer is a mock of UnsafeConsentServer interface.
type MockUnsafeConsentServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeConsentServerMockRecorder
}

// MockUnsafeConsentServerMockRecorder is the mock recorder for MockUnsafeConsentServer.
type MockUnsafeConsentServerMockRecorder struct {
	mock *MockUnsafeConsentServer
}

// NewMockUnsafeConsentServer creates a new mock instance.
func NewMockUnsafeConsentServer(ctrl *gomock.Controller) *MockUnsafeConsentServer {
	mock := &MockUnsafeConsentServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeConsentServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeConsentServer) EXPECT() *MockUnsafeConsentServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedConsentServer mocks base method.
func (m *MockUnsafeConsentServer) mustEmbedUnimplementedConsentServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedConsentServer")
}

// mustEmbedUnimplementedConsentServer indicates an expected call of mustEmbedUnimplementedConsentServer.
func (mr *MockUnsafeConsentServerMockRecorder) mustEmbedUnimplementedConsentServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedConsentServer", reflect.TypeOf((*MockUnsafeConsentServer)(nil).mustEmbedUnimplementedConsentServer))
}
