// Code generated by MockGen. DO NOT EDIT.
// Source: api/webfe/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	webfe "github.com/epifi/gamma/api/webfe"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockWebfeClient is a mock of WebfeClient interface.
type MockWebfeClient struct {
	ctrl     *gomock.Controller
	recorder *MockWebfeClientMockRecorder
}

// MockWebfeClientMockRecorder is the mock recorder for MockWebfeClient.
type MockWebfeClientMockRecorder struct {
	mock *MockWebfeClient
}

// NewMockWebfeClient creates a new mock instance.
func NewMockWebfeClient(ctrl *gomock.Controller) *MockWebfeClient {
	mock := &MockWebfeClient{ctrl: ctrl}
	mock.recorder = &MockWebfeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWebfeClient) EXPECT() *MockWebfeClientMockRecorder {
	return m.recorder
}

// CheckCreditCardEligibility mocks base method.
func (m *MockWebfeClient) CheckCreditCardEligibility(ctx context.Context, in *webfe.CheckCreditCardEligibilityRequest, opts ...grpc.CallOption) (*webfe.CheckCreditCardEligibilityResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckCreditCardEligibility", varargs...)
	ret0, _ := ret[0].(*webfe.CheckCreditCardEligibilityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCreditCardEligibility indicates an expected call of CheckCreditCardEligibility.
func (mr *MockWebfeClientMockRecorder) CheckCreditCardEligibility(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCreditCardEligibility", reflect.TypeOf((*MockWebfeClient)(nil).CheckCreditCardEligibility), varargs...)
}

// GenerateEmailOtp mocks base method.
func (m *MockWebfeClient) GenerateEmailOtp(ctx context.Context, in *webfe.GenerateEmailOtpRequest, opts ...grpc.CallOption) (*webfe.GenerateEmailOtpResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateEmailOtp", varargs...)
	ret0, _ := ret[0].(*webfe.GenerateEmailOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateEmailOtp indicates an expected call of GenerateEmailOtp.
func (mr *MockWebfeClientMockRecorder) GenerateEmailOtp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateEmailOtp", reflect.TypeOf((*MockWebfeClient)(nil).GenerateEmailOtp), varargs...)
}

// GeneratePhoneOtp mocks base method.
func (m *MockWebfeClient) GeneratePhoneOtp(ctx context.Context, in *webfe.GeneratePhoneOtpRequest, opts ...grpc.CallOption) (*webfe.GeneratePhoneOtpResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GeneratePhoneOtp", varargs...)
	ret0, _ := ret[0].(*webfe.GeneratePhoneOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GeneratePhoneOtp indicates an expected call of GeneratePhoneOtp.
func (mr *MockWebfeClientMockRecorder) GeneratePhoneOtp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneratePhoneOtp", reflect.TypeOf((*MockWebfeClient)(nil).GeneratePhoneOtp), varargs...)
}

// GetRequestStatus mocks base method.
func (m *MockWebfeClient) GetRequestStatus(ctx context.Context, in *webfe.GetRequestStatusRequest, opts ...grpc.CallOption) (*webfe.GetRequestStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRequestStatus", varargs...)
	ret0, _ := ret[0].(*webfe.GetRequestStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRequestStatus indicates an expected call of GetRequestStatus.
func (mr *MockWebfeClientMockRecorder) GetRequestStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRequestStatus", reflect.TypeOf((*MockWebfeClient)(nil).GetRequestStatus), varargs...)
}

// SendAppLinkToUser mocks base method.
func (m *MockWebfeClient) SendAppLinkToUser(ctx context.Context, in *webfe.SendAppLinkToUserRequest, opts ...grpc.CallOption) (*webfe.SendAppLinkToUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendAppLinkToUser", varargs...)
	ret0, _ := ret[0].(*webfe.SendAppLinkToUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendAppLinkToUser indicates an expected call of SendAppLinkToUser.
func (mr *MockWebfeClientMockRecorder) SendAppLinkToUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAppLinkToUser", reflect.TypeOf((*MockWebfeClient)(nil).SendAppLinkToUser), varargs...)
}

// ValidateLogin mocks base method.
func (m *MockWebfeClient) ValidateLogin(ctx context.Context, in *webfe.ValidateLoginRequest, opts ...grpc.CallOption) (*webfe.ValidateLoginResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ValidateLogin", varargs...)
	ret0, _ := ret[0].(*webfe.ValidateLoginResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateLogin indicates an expected call of ValidateLogin.
func (mr *MockWebfeClientMockRecorder) ValidateLogin(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateLogin", reflect.TypeOf((*MockWebfeClient)(nil).ValidateLogin), varargs...)
}

// VerifyEmailOtp mocks base method.
func (m *MockWebfeClient) VerifyEmailOtp(ctx context.Context, in *webfe.VerifyEmailOtpRequest, opts ...grpc.CallOption) (*webfe.VerifyEmailOtpResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyEmailOtp", varargs...)
	ret0, _ := ret[0].(*webfe.VerifyEmailOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyEmailOtp indicates an expected call of VerifyEmailOtp.
func (mr *MockWebfeClientMockRecorder) VerifyEmailOtp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyEmailOtp", reflect.TypeOf((*MockWebfeClient)(nil).VerifyEmailOtp), varargs...)
}

// VerifyPhoneOtp mocks base method.
func (m *MockWebfeClient) VerifyPhoneOtp(ctx context.Context, in *webfe.VerifyPhoneOtpRequest, opts ...grpc.CallOption) (*webfe.VerifyPhoneOtpResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyPhoneOtp", varargs...)
	ret0, _ := ret[0].(*webfe.VerifyPhoneOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyPhoneOtp indicates an expected call of VerifyPhoneOtp.
func (mr *MockWebfeClientMockRecorder) VerifyPhoneOtp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyPhoneOtp", reflect.TypeOf((*MockWebfeClient)(nil).VerifyPhoneOtp), varargs...)
}

// MockWebfeServer is a mock of WebfeServer interface.
type MockWebfeServer struct {
	ctrl     *gomock.Controller
	recorder *MockWebfeServerMockRecorder
}

// MockWebfeServerMockRecorder is the mock recorder for MockWebfeServer.
type MockWebfeServerMockRecorder struct {
	mock *MockWebfeServer
}

// NewMockWebfeServer creates a new mock instance.
func NewMockWebfeServer(ctrl *gomock.Controller) *MockWebfeServer {
	mock := &MockWebfeServer{ctrl: ctrl}
	mock.recorder = &MockWebfeServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWebfeServer) EXPECT() *MockWebfeServerMockRecorder {
	return m.recorder
}

// CheckCreditCardEligibility mocks base method.
func (m *MockWebfeServer) CheckCreditCardEligibility(arg0 context.Context, arg1 *webfe.CheckCreditCardEligibilityRequest) (*webfe.CheckCreditCardEligibilityResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckCreditCardEligibility", arg0, arg1)
	ret0, _ := ret[0].(*webfe.CheckCreditCardEligibilityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckCreditCardEligibility indicates an expected call of CheckCreditCardEligibility.
func (mr *MockWebfeServerMockRecorder) CheckCreditCardEligibility(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckCreditCardEligibility", reflect.TypeOf((*MockWebfeServer)(nil).CheckCreditCardEligibility), arg0, arg1)
}

// GenerateEmailOtp mocks base method.
func (m *MockWebfeServer) GenerateEmailOtp(arg0 context.Context, arg1 *webfe.GenerateEmailOtpRequest) (*webfe.GenerateEmailOtpResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateEmailOtp", arg0, arg1)
	ret0, _ := ret[0].(*webfe.GenerateEmailOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateEmailOtp indicates an expected call of GenerateEmailOtp.
func (mr *MockWebfeServerMockRecorder) GenerateEmailOtp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateEmailOtp", reflect.TypeOf((*MockWebfeServer)(nil).GenerateEmailOtp), arg0, arg1)
}

// GeneratePhoneOtp mocks base method.
func (m *MockWebfeServer) GeneratePhoneOtp(arg0 context.Context, arg1 *webfe.GeneratePhoneOtpRequest) (*webfe.GeneratePhoneOtpResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GeneratePhoneOtp", arg0, arg1)
	ret0, _ := ret[0].(*webfe.GeneratePhoneOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GeneratePhoneOtp indicates an expected call of GeneratePhoneOtp.
func (mr *MockWebfeServerMockRecorder) GeneratePhoneOtp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneratePhoneOtp", reflect.TypeOf((*MockWebfeServer)(nil).GeneratePhoneOtp), arg0, arg1)
}

// GetRequestStatus mocks base method.
func (m *MockWebfeServer) GetRequestStatus(arg0 context.Context, arg1 *webfe.GetRequestStatusRequest) (*webfe.GetRequestStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRequestStatus", arg0, arg1)
	ret0, _ := ret[0].(*webfe.GetRequestStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRequestStatus indicates an expected call of GetRequestStatus.
func (mr *MockWebfeServerMockRecorder) GetRequestStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRequestStatus", reflect.TypeOf((*MockWebfeServer)(nil).GetRequestStatus), arg0, arg1)
}

// SendAppLinkToUser mocks base method.
func (m *MockWebfeServer) SendAppLinkToUser(arg0 context.Context, arg1 *webfe.SendAppLinkToUserRequest) (*webfe.SendAppLinkToUserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendAppLinkToUser", arg0, arg1)
	ret0, _ := ret[0].(*webfe.SendAppLinkToUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendAppLinkToUser indicates an expected call of SendAppLinkToUser.
func (mr *MockWebfeServerMockRecorder) SendAppLinkToUser(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAppLinkToUser", reflect.TypeOf((*MockWebfeServer)(nil).SendAppLinkToUser), arg0, arg1)
}

// ValidateLogin mocks base method.
func (m *MockWebfeServer) ValidateLogin(arg0 context.Context, arg1 *webfe.ValidateLoginRequest) (*webfe.ValidateLoginResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateLogin", arg0, arg1)
	ret0, _ := ret[0].(*webfe.ValidateLoginResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateLogin indicates an expected call of ValidateLogin.
func (mr *MockWebfeServerMockRecorder) ValidateLogin(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateLogin", reflect.TypeOf((*MockWebfeServer)(nil).ValidateLogin), arg0, arg1)
}

// VerifyEmailOtp mocks base method.
func (m *MockWebfeServer) VerifyEmailOtp(arg0 context.Context, arg1 *webfe.VerifyEmailOtpRequest) (*webfe.VerifyEmailOtpResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyEmailOtp", arg0, arg1)
	ret0, _ := ret[0].(*webfe.VerifyEmailOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyEmailOtp indicates an expected call of VerifyEmailOtp.
func (mr *MockWebfeServerMockRecorder) VerifyEmailOtp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyEmailOtp", reflect.TypeOf((*MockWebfeServer)(nil).VerifyEmailOtp), arg0, arg1)
}

// VerifyPhoneOtp mocks base method.
func (m *MockWebfeServer) VerifyPhoneOtp(arg0 context.Context, arg1 *webfe.VerifyPhoneOtpRequest) (*webfe.VerifyPhoneOtpResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyPhoneOtp", arg0, arg1)
	ret0, _ := ret[0].(*webfe.VerifyPhoneOtpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyPhoneOtp indicates an expected call of VerifyPhoneOtp.
func (mr *MockWebfeServerMockRecorder) VerifyPhoneOtp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyPhoneOtp", reflect.TypeOf((*MockWebfeServer)(nil).VerifyPhoneOtp), arg0, arg1)
}

// MockUnsafeWebfeServer is a mock of UnsafeWebfeServer interface.
type MockUnsafeWebfeServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeWebfeServerMockRecorder
}

// MockUnsafeWebfeServerMockRecorder is the mock recorder for MockUnsafeWebfeServer.
type MockUnsafeWebfeServerMockRecorder struct {
	mock *MockUnsafeWebfeServer
}

// NewMockUnsafeWebfeServer creates a new mock instance.
func NewMockUnsafeWebfeServer(ctrl *gomock.Controller) *MockUnsafeWebfeServer {
	mock := &MockUnsafeWebfeServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeWebfeServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeWebfeServer) EXPECT() *MockUnsafeWebfeServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedWebfeServer mocks base method.
func (m *MockUnsafeWebfeServer) mustEmbedUnimplementedWebfeServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedWebfeServer")
}

// mustEmbedUnimplementedWebfeServer indicates an expected call of mustEmbedUnimplementedWebfeServer.
func (mr *MockUnsafeWebfeServerMockRecorder) mustEmbedUnimplementedWebfeServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedWebfeServer", reflect.TypeOf((*MockUnsafeWebfeServer)(nil).mustEmbedUnimplementedWebfeServer))
}
