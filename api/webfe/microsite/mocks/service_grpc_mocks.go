// Code generated by MockGen. DO NOT EDIT.
// Source: api/webfe/microsite/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	microsite "github.com/epifi/gamma/api/webfe/microsite"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockMicroSiteClient is a mock of MicroSiteClient interface.
type MockMicroSiteClient struct {
	ctrl     *gomock.Controller
	recorder *MockMicroSiteClientMockRecorder
}

// MockMicroSiteClientMockRecorder is the mock recorder for MockMicroSiteClient.
type MockMicroSiteClientMockRecorder struct {
	mock *MockMicroSiteClient
}

// NewMockMicroSiteClient creates a new mock instance.
func NewMockMicroSiteClient(ctrl *gomock.Controller) *MockMicroSiteClient {
	mock := &MockMicroSiteClient{ctrl: ctrl}
	mock.recorder = &MockMicroSiteClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMicroSiteClient) EXPECT() *MockMicroSiteClientMockRecorder {
	return m.recorder
}

// SendRentReceiptToUserEmail mocks base method.
func (m *MockMicroSiteClient) SendRentReceiptToUserEmail(ctx context.Context, in *microsite.SendRentReceiptToUserEmailRequest, opts ...grpc.CallOption) (*microsite.SendRentReceiptToUserEmailResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendRentReceiptToUserEmail", varargs...)
	ret0, _ := ret[0].(*microsite.SendRentReceiptToUserEmailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendRentReceiptToUserEmail indicates an expected call of SendRentReceiptToUserEmail.
func (mr *MockMicroSiteClientMockRecorder) SendRentReceiptToUserEmail(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendRentReceiptToUserEmail", reflect.TypeOf((*MockMicroSiteClient)(nil).SendRentReceiptToUserEmail), varargs...)
}

// MockMicroSiteServer is a mock of MicroSiteServer interface.
type MockMicroSiteServer struct {
	ctrl     *gomock.Controller
	recorder *MockMicroSiteServerMockRecorder
}

// MockMicroSiteServerMockRecorder is the mock recorder for MockMicroSiteServer.
type MockMicroSiteServerMockRecorder struct {
	mock *MockMicroSiteServer
}

// NewMockMicroSiteServer creates a new mock instance.
func NewMockMicroSiteServer(ctrl *gomock.Controller) *MockMicroSiteServer {
	mock := &MockMicroSiteServer{ctrl: ctrl}
	mock.recorder = &MockMicroSiteServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMicroSiteServer) EXPECT() *MockMicroSiteServerMockRecorder {
	return m.recorder
}

// SendRentReceiptToUserEmail mocks base method.
func (m *MockMicroSiteServer) SendRentReceiptToUserEmail(arg0 context.Context, arg1 *microsite.SendRentReceiptToUserEmailRequest) (*microsite.SendRentReceiptToUserEmailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendRentReceiptToUserEmail", arg0, arg1)
	ret0, _ := ret[0].(*microsite.SendRentReceiptToUserEmailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendRentReceiptToUserEmail indicates an expected call of SendRentReceiptToUserEmail.
func (mr *MockMicroSiteServerMockRecorder) SendRentReceiptToUserEmail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendRentReceiptToUserEmail", reflect.TypeOf((*MockMicroSiteServer)(nil).SendRentReceiptToUserEmail), arg0, arg1)
}

// MockUnsafeMicroSiteServer is a mock of UnsafeMicroSiteServer interface.
type MockUnsafeMicroSiteServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeMicroSiteServerMockRecorder
}

// MockUnsafeMicroSiteServerMockRecorder is the mock recorder for MockUnsafeMicroSiteServer.
type MockUnsafeMicroSiteServerMockRecorder struct {
	mock *MockUnsafeMicroSiteServer
}

// NewMockUnsafeMicroSiteServer creates a new mock instance.
func NewMockUnsafeMicroSiteServer(ctrl *gomock.Controller) *MockUnsafeMicroSiteServer {
	mock := &MockUnsafeMicroSiteServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeMicroSiteServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeMicroSiteServer) EXPECT() *MockUnsafeMicroSiteServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedMicroSiteServer mocks base method.
func (m *MockUnsafeMicroSiteServer) mustEmbedUnimplementedMicroSiteServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedMicroSiteServer")
}

// mustEmbedUnimplementedMicroSiteServer indicates an expected call of mustEmbedUnimplementedMicroSiteServer.
func (mr *MockUnsafeMicroSiteServerMockRecorder) mustEmbedUnimplementedMicroSiteServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedMicroSiteServer", reflect.TypeOf((*MockUnsafeMicroSiteServer)(nil).mustEmbedUnimplementedMicroSiteServer))
}
