// Code generated by MockGen. DO NOT EDIT.
// Source: api/webfe/loanseligibility/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	loanseligibility "github.com/epifi/gamma/api/webfe/loanseligibility"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockLoansEligibilityClient is a mock of LoansEligibilityClient interface.
type MockLoansEligibilityClient struct {
	ctrl     *gomock.Controller
	recorder *MockLoansEligibilityClientMockRecorder
}

// MockLoansEligibilityClientMockRecorder is the mock recorder for MockLoansEligibilityClient.
type MockLoansEligibilityClientMockRecorder struct {
	mock *MockLoansEligibilityClient
}

// NewMockLoansEligibilityClient creates a new mock instance.
func NewMockLoansEligibilityClient(ctrl *gomock.Controller) *MockLoansEligibilityClient {
	mock := &MockLoansEligibilityClient{ctrl: ctrl}
	mock.recorder = &MockLoansEligibilityClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoansEligibilityClient) EXPECT() *MockLoansEligibilityClientMockRecorder {
	return m.recorder
}

// CollectUserDetails mocks base method.
func (m *MockLoansEligibilityClient) CollectUserDetails(ctx context.Context, in *loanseligibility.CollectUserDetailsRequest, opts ...grpc.CallOption) (*loanseligibility.CollectUserDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CollectUserDetails", varargs...)
	ret0, _ := ret[0].(*loanseligibility.CollectUserDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CollectUserDetails indicates an expected call of CollectUserDetails.
func (mr *MockLoansEligibilityClientMockRecorder) CollectUserDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CollectUserDetails", reflect.TypeOf((*MockLoansEligibilityClient)(nil).CollectUserDetails), varargs...)
}

// GetFiPromotionDetails mocks base method.
func (m *MockLoansEligibilityClient) GetFiPromotionDetails(ctx context.Context, in *loanseligibility.GetFiPromotionDetailsRequest, opts ...grpc.CallOption) (*loanseligibility.GetFiPromotionDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFiPromotionDetails", varargs...)
	ret0, _ := ret[0].(*loanseligibility.GetFiPromotionDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFiPromotionDetails indicates an expected call of GetFiPromotionDetails.
func (mr *MockLoansEligibilityClientMockRecorder) GetFiPromotionDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFiPromotionDetails", reflect.TypeOf((*MockLoansEligibilityClient)(nil).GetFiPromotionDetails), varargs...)
}

// GetLoansLandingPageDetails mocks base method.
func (m *MockLoansEligibilityClient) GetLoansLandingPageDetails(ctx context.Context, in *loanseligibility.GetLoansLandingPageDetailsRequest, opts ...grpc.CallOption) (*loanseligibility.GetLoansLandingPageDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoansLandingPageDetails", varargs...)
	ret0, _ := ret[0].(*loanseligibility.GetLoansLandingPageDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoansLandingPageDetails indicates an expected call of GetLoansLandingPageDetails.
func (mr *MockLoansEligibilityClientMockRecorder) GetLoansLandingPageDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoansLandingPageDetails", reflect.TypeOf((*MockLoansEligibilityClient)(nil).GetLoansLandingPageDetails), varargs...)
}

// GetLoansNextEligibilityStep mocks base method.
func (m *MockLoansEligibilityClient) GetLoansNextEligibilityStep(ctx context.Context, in *loanseligibility.GetLoansNextEligibilityStepRequest, opts ...grpc.CallOption) (*loanseligibility.GetLoansNextEligibilityStepResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoansNextEligibilityStep", varargs...)
	ret0, _ := ret[0].(*loanseligibility.GetLoansNextEligibilityStepResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoansNextEligibilityStep indicates an expected call of GetLoansNextEligibilityStep.
func (mr *MockLoansEligibilityClientMockRecorder) GetLoansNextEligibilityStep(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoansNextEligibilityStep", reflect.TypeOf((*MockLoansEligibilityClient)(nil).GetLoansNextEligibilityStep), varargs...)
}

// MockLoansEligibilityServer is a mock of LoansEligibilityServer interface.
type MockLoansEligibilityServer struct {
	ctrl     *gomock.Controller
	recorder *MockLoansEligibilityServerMockRecorder
}

// MockLoansEligibilityServerMockRecorder is the mock recorder for MockLoansEligibilityServer.
type MockLoansEligibilityServerMockRecorder struct {
	mock *MockLoansEligibilityServer
}

// NewMockLoansEligibilityServer creates a new mock instance.
func NewMockLoansEligibilityServer(ctrl *gomock.Controller) *MockLoansEligibilityServer {
	mock := &MockLoansEligibilityServer{ctrl: ctrl}
	mock.recorder = &MockLoansEligibilityServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoansEligibilityServer) EXPECT() *MockLoansEligibilityServerMockRecorder {
	return m.recorder
}

// CollectUserDetails mocks base method.
func (m *MockLoansEligibilityServer) CollectUserDetails(arg0 context.Context, arg1 *loanseligibility.CollectUserDetailsRequest) (*loanseligibility.CollectUserDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CollectUserDetails", arg0, arg1)
	ret0, _ := ret[0].(*loanseligibility.CollectUserDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CollectUserDetails indicates an expected call of CollectUserDetails.
func (mr *MockLoansEligibilityServerMockRecorder) CollectUserDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CollectUserDetails", reflect.TypeOf((*MockLoansEligibilityServer)(nil).CollectUserDetails), arg0, arg1)
}

// GetFiPromotionDetails mocks base method.
func (m *MockLoansEligibilityServer) GetFiPromotionDetails(arg0 context.Context, arg1 *loanseligibility.GetFiPromotionDetailsRequest) (*loanseligibility.GetFiPromotionDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFiPromotionDetails", arg0, arg1)
	ret0, _ := ret[0].(*loanseligibility.GetFiPromotionDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFiPromotionDetails indicates an expected call of GetFiPromotionDetails.
func (mr *MockLoansEligibilityServerMockRecorder) GetFiPromotionDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFiPromotionDetails", reflect.TypeOf((*MockLoansEligibilityServer)(nil).GetFiPromotionDetails), arg0, arg1)
}

// GetLoansLandingPageDetails mocks base method.
func (m *MockLoansEligibilityServer) GetLoansLandingPageDetails(arg0 context.Context, arg1 *loanseligibility.GetLoansLandingPageDetailsRequest) (*loanseligibility.GetLoansLandingPageDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoansLandingPageDetails", arg0, arg1)
	ret0, _ := ret[0].(*loanseligibility.GetLoansLandingPageDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoansLandingPageDetails indicates an expected call of GetLoansLandingPageDetails.
func (mr *MockLoansEligibilityServerMockRecorder) GetLoansLandingPageDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoansLandingPageDetails", reflect.TypeOf((*MockLoansEligibilityServer)(nil).GetLoansLandingPageDetails), arg0, arg1)
}

// GetLoansNextEligibilityStep mocks base method.
func (m *MockLoansEligibilityServer) GetLoansNextEligibilityStep(arg0 context.Context, arg1 *loanseligibility.GetLoansNextEligibilityStepRequest) (*loanseligibility.GetLoansNextEligibilityStepResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoansNextEligibilityStep", arg0, arg1)
	ret0, _ := ret[0].(*loanseligibility.GetLoansNextEligibilityStepResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoansNextEligibilityStep indicates an expected call of GetLoansNextEligibilityStep.
func (mr *MockLoansEligibilityServerMockRecorder) GetLoansNextEligibilityStep(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoansNextEligibilityStep", reflect.TypeOf((*MockLoansEligibilityServer)(nil).GetLoansNextEligibilityStep), arg0, arg1)
}

// MockUnsafeLoansEligibilityServer is a mock of UnsafeLoansEligibilityServer interface.
type MockUnsafeLoansEligibilityServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeLoansEligibilityServerMockRecorder
}

// MockUnsafeLoansEligibilityServerMockRecorder is the mock recorder for MockUnsafeLoansEligibilityServer.
type MockUnsafeLoansEligibilityServerMockRecorder struct {
	mock *MockUnsafeLoansEligibilityServer
}

// NewMockUnsafeLoansEligibilityServer creates a new mock instance.
func NewMockUnsafeLoansEligibilityServer(ctrl *gomock.Controller) *MockUnsafeLoansEligibilityServer {
	mock := &MockUnsafeLoansEligibilityServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeLoansEligibilityServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeLoansEligibilityServer) EXPECT() *MockUnsafeLoansEligibilityServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedLoansEligibilityServer mocks base method.
func (m *MockUnsafeLoansEligibilityServer) mustEmbedUnimplementedLoansEligibilityServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedLoansEligibilityServer")
}

// mustEmbedUnimplementedLoansEligibilityServer indicates an expected call of mustEmbedUnimplementedLoansEligibilityServer.
func (mr *MockUnsafeLoansEligibilityServerMockRecorder) mustEmbedUnimplementedLoansEligibilityServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedLoansEligibilityServer", reflect.TypeOf((*MockUnsafeLoansEligibilityServer)(nil).mustEmbedUnimplementedLoansEligibilityServer))
}
