// Code generated by MockGen. DO NOT EDIT.
// Source: api/webfe/risk/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	risk "github.com/epifi/gamma/api/webfe/risk"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockRiskClient is a mock of RiskClient interface.
type MockRiskClient struct {
	ctrl     *gomock.Controller
	recorder *MockRiskClientMockRecorder
}

// MockRiskClientMockRecorder is the mock recorder for MockRiskClient.
type MockRiskClientMockRecorder struct {
	mock *MockRiskClient
}

// NewMockRiskClient creates a new mock instance.
func NewMockRiskClient(ctrl *gomock.Controller) *MockRiskClient {
	mock := &MockRiskClient{ctrl: ctrl}
	mock.recorder = &MockRiskClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskClient) EXPECT() *MockRiskClientMockRecorder {
	return m.recorder
}

// GetForm mocks base method.
func (m *MockRiskClient) GetForm(ctx context.Context, in *risk.GetFormRequest, opts ...grpc.CallOption) (*risk.GetFormResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetForm", varargs...)
	ret0, _ := ret[0].(*risk.GetFormResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForm indicates an expected call of GetForm.
func (mr *MockRiskClientMockRecorder) GetForm(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForm", reflect.TypeOf((*MockRiskClient)(nil).GetForm), varargs...)
}

// SubmitForm mocks base method.
func (m *MockRiskClient) SubmitForm(ctx context.Context, in *risk.SubmitFormRequest, opts ...grpc.CallOption) (*risk.SubmitFormResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitForm", varargs...)
	ret0, _ := ret[0].(*risk.SubmitFormResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitForm indicates an expected call of SubmitForm.
func (mr *MockRiskClientMockRecorder) SubmitForm(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitForm", reflect.TypeOf((*MockRiskClient)(nil).SubmitForm), varargs...)
}

// MockRiskServer is a mock of RiskServer interface.
type MockRiskServer struct {
	ctrl     *gomock.Controller
	recorder *MockRiskServerMockRecorder
}

// MockRiskServerMockRecorder is the mock recorder for MockRiskServer.
type MockRiskServerMockRecorder struct {
	mock *MockRiskServer
}

// NewMockRiskServer creates a new mock instance.
func NewMockRiskServer(ctrl *gomock.Controller) *MockRiskServer {
	mock := &MockRiskServer{ctrl: ctrl}
	mock.recorder = &MockRiskServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskServer) EXPECT() *MockRiskServerMockRecorder {
	return m.recorder
}

// GetForm mocks base method.
func (m *MockRiskServer) GetForm(arg0 context.Context, arg1 *risk.GetFormRequest) (*risk.GetFormResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetForm", arg0, arg1)
	ret0, _ := ret[0].(*risk.GetFormResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForm indicates an expected call of GetForm.
func (mr *MockRiskServerMockRecorder) GetForm(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForm", reflect.TypeOf((*MockRiskServer)(nil).GetForm), arg0, arg1)
}

// SubmitForm mocks base method.
func (m *MockRiskServer) SubmitForm(arg0 context.Context, arg1 *risk.SubmitFormRequest) (*risk.SubmitFormResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitForm", arg0, arg1)
	ret0, _ := ret[0].(*risk.SubmitFormResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitForm indicates an expected call of SubmitForm.
func (mr *MockRiskServerMockRecorder) SubmitForm(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitForm", reflect.TypeOf((*MockRiskServer)(nil).SubmitForm), arg0, arg1)
}

// MockUnsafeRiskServer is a mock of UnsafeRiskServer interface.
type MockUnsafeRiskServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeRiskServerMockRecorder
}

// MockUnsafeRiskServerMockRecorder is the mock recorder for MockUnsafeRiskServer.
type MockUnsafeRiskServerMockRecorder struct {
	mock *MockUnsafeRiskServer
}

// NewMockUnsafeRiskServer creates a new mock instance.
func NewMockUnsafeRiskServer(ctrl *gomock.Controller) *MockUnsafeRiskServer {
	mock := &MockUnsafeRiskServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeRiskServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeRiskServer) EXPECT() *MockUnsafeRiskServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedRiskServer mocks base method.
func (m *MockUnsafeRiskServer) mustEmbedUnimplementedRiskServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedRiskServer")
}

// mustEmbedUnimplementedRiskServer indicates an expected call of mustEmbedUnimplementedRiskServer.
func (mr *MockUnsafeRiskServerMockRecorder) mustEmbedUnimplementedRiskServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedRiskServer", reflect.TypeOf((*MockUnsafeRiskServer)(nil).mustEmbedUnimplementedRiskServer))
}
