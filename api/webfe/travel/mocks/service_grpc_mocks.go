// Code generated by MockGen. DO NOT EDIT.
// Source: api/webfe/travel/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	travel "github.com/epifi/gamma/api/webfe/travel"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockTravelBudgetClient is a mock of TravelBudgetClient interface.
type MockTravelBudgetClient struct {
	ctrl     *gomock.Controller
	recorder *MockTravelBudgetClientMockRecorder
}

// MockTravelBudgetClientMockRecorder is the mock recorder for MockTravelBudgetClient.
type MockTravelBudgetClientMockRecorder struct {
	mock *MockTravelBudgetClient
}

// NewMockTravelBudgetClient creates a new mock instance.
func NewMockTravelBudgetClient(ctrl *gomock.Controller) *MockTravelBudgetClient {
	mock := &MockTravelBudgetClient{ctrl: ctrl}
	mock.recorder = &MockTravelBudgetClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTravelBudgetClient) EXPECT() *MockTravelBudgetClientMockRecorder {
	return m.recorder
}

// GetForexExchangeRate mocks base method.
func (m *MockTravelBudgetClient) GetForexExchangeRate(ctx context.Context, in *travel.GetForexExchangeRateRequest, opts ...grpc.CallOption) (*travel.GetForexExchangeRateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetForexExchangeRate", varargs...)
	ret0, _ := ret[0].(*travel.GetForexExchangeRateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForexExchangeRate indicates an expected call of GetForexExchangeRate.
func (mr *MockTravelBudgetClientMockRecorder) GetForexExchangeRate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForexExchangeRate", reflect.TypeOf((*MockTravelBudgetClient)(nil).GetForexExchangeRate), varargs...)
}

// GetInternationalATMWithdrawalLimits mocks base method.
func (m *MockTravelBudgetClient) GetInternationalATMWithdrawalLimits(ctx context.Context, in *travel.GetInternationalATMWithdrawalLimitsRequest, opts ...grpc.CallOption) (*travel.GetInternationalATMWithdrawalLimitsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInternationalATMWithdrawalLimits", varargs...)
	ret0, _ := ret[0].(*travel.GetInternationalATMWithdrawalLimitsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInternationalATMWithdrawalLimits indicates an expected call of GetInternationalATMWithdrawalLimits.
func (mr *MockTravelBudgetClientMockRecorder) GetInternationalATMWithdrawalLimits(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInternationalATMWithdrawalLimits", reflect.TypeOf((*MockTravelBudgetClient)(nil).GetInternationalATMWithdrawalLimits), varargs...)
}

// GetTravelDestinations mocks base method.
func (m *MockTravelBudgetClient) GetTravelDestinations(ctx context.Context, in *travel.GetTravelDestinationsRequest, opts ...grpc.CallOption) (*travel.GetTravelDestinationsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTravelDestinations", varargs...)
	ret0, _ := ret[0].(*travel.GetTravelDestinationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTravelDestinations indicates an expected call of GetTravelDestinations.
func (mr *MockTravelBudgetClientMockRecorder) GetTravelDestinations(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTravelDestinations", reflect.TypeOf((*MockTravelBudgetClient)(nil).GetTravelDestinations), varargs...)
}

// GetTravelExpense mocks base method.
func (m *MockTravelBudgetClient) GetTravelExpense(ctx context.Context, in *travel.GetTravelExpenseRequest, opts ...grpc.CallOption) (*travel.GetTravelExpenseResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTravelExpense", varargs...)
	ret0, _ := ret[0].(*travel.GetTravelExpenseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTravelExpense indicates an expected call of GetTravelExpense.
func (mr *MockTravelBudgetClientMockRecorder) GetTravelExpense(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTravelExpense", reflect.TypeOf((*MockTravelBudgetClient)(nil).GetTravelExpense), varargs...)
}

// MockTravelBudgetServer is a mock of TravelBudgetServer interface.
type MockTravelBudgetServer struct {
	ctrl     *gomock.Controller
	recorder *MockTravelBudgetServerMockRecorder
}

// MockTravelBudgetServerMockRecorder is the mock recorder for MockTravelBudgetServer.
type MockTravelBudgetServerMockRecorder struct {
	mock *MockTravelBudgetServer
}

// NewMockTravelBudgetServer creates a new mock instance.
func NewMockTravelBudgetServer(ctrl *gomock.Controller) *MockTravelBudgetServer {
	mock := &MockTravelBudgetServer{ctrl: ctrl}
	mock.recorder = &MockTravelBudgetServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTravelBudgetServer) EXPECT() *MockTravelBudgetServerMockRecorder {
	return m.recorder
}

// GetForexExchangeRate mocks base method.
func (m *MockTravelBudgetServer) GetForexExchangeRate(arg0 context.Context, arg1 *travel.GetForexExchangeRateRequest) (*travel.GetForexExchangeRateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetForexExchangeRate", arg0, arg1)
	ret0, _ := ret[0].(*travel.GetForexExchangeRateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForexExchangeRate indicates an expected call of GetForexExchangeRate.
func (mr *MockTravelBudgetServerMockRecorder) GetForexExchangeRate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForexExchangeRate", reflect.TypeOf((*MockTravelBudgetServer)(nil).GetForexExchangeRate), arg0, arg1)
}

// GetInternationalATMWithdrawalLimits mocks base method.
func (m *MockTravelBudgetServer) GetInternationalATMWithdrawalLimits(arg0 context.Context, arg1 *travel.GetInternationalATMWithdrawalLimitsRequest) (*travel.GetInternationalATMWithdrawalLimitsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInternationalATMWithdrawalLimits", arg0, arg1)
	ret0, _ := ret[0].(*travel.GetInternationalATMWithdrawalLimitsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInternationalATMWithdrawalLimits indicates an expected call of GetInternationalATMWithdrawalLimits.
func (mr *MockTravelBudgetServerMockRecorder) GetInternationalATMWithdrawalLimits(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInternationalATMWithdrawalLimits", reflect.TypeOf((*MockTravelBudgetServer)(nil).GetInternationalATMWithdrawalLimits), arg0, arg1)
}

// GetTravelDestinations mocks base method.
func (m *MockTravelBudgetServer) GetTravelDestinations(arg0 context.Context, arg1 *travel.GetTravelDestinationsRequest) (*travel.GetTravelDestinationsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTravelDestinations", arg0, arg1)
	ret0, _ := ret[0].(*travel.GetTravelDestinationsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTravelDestinations indicates an expected call of GetTravelDestinations.
func (mr *MockTravelBudgetServerMockRecorder) GetTravelDestinations(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTravelDestinations", reflect.TypeOf((*MockTravelBudgetServer)(nil).GetTravelDestinations), arg0, arg1)
}

// GetTravelExpense mocks base method.
func (m *MockTravelBudgetServer) GetTravelExpense(arg0 context.Context, arg1 *travel.GetTravelExpenseRequest) (*travel.GetTravelExpenseResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTravelExpense", arg0, arg1)
	ret0, _ := ret[0].(*travel.GetTravelExpenseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTravelExpense indicates an expected call of GetTravelExpense.
func (mr *MockTravelBudgetServerMockRecorder) GetTravelExpense(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTravelExpense", reflect.TypeOf((*MockTravelBudgetServer)(nil).GetTravelExpense), arg0, arg1)
}

// MockUnsafeTravelBudgetServer is a mock of UnsafeTravelBudgetServer interface.
type MockUnsafeTravelBudgetServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeTravelBudgetServerMockRecorder
}

// MockUnsafeTravelBudgetServerMockRecorder is the mock recorder for MockUnsafeTravelBudgetServer.
type MockUnsafeTravelBudgetServerMockRecorder struct {
	mock *MockUnsafeTravelBudgetServer
}

// NewMockUnsafeTravelBudgetServer creates a new mock instance.
func NewMockUnsafeTravelBudgetServer(ctrl *gomock.Controller) *MockUnsafeTravelBudgetServer {
	mock := &MockUnsafeTravelBudgetServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeTravelBudgetServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeTravelBudgetServer) EXPECT() *MockUnsafeTravelBudgetServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedTravelBudgetServer mocks base method.
func (m *MockUnsafeTravelBudgetServer) mustEmbedUnimplementedTravelBudgetServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTravelBudgetServer")
}

// mustEmbedUnimplementedTravelBudgetServer indicates an expected call of mustEmbedUnimplementedTravelBudgetServer.
func (mr *MockUnsafeTravelBudgetServerMockRecorder) mustEmbedUnimplementedTravelBudgetServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTravelBudgetServer", reflect.TypeOf((*MockUnsafeTravelBudgetServer)(nil).mustEmbedUnimplementedTravelBudgetServer))
}
