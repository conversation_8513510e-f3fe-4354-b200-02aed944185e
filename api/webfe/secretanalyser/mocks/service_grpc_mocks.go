// Code generated by MockGen. DO NOT EDIT.
// Source: api/webfe/secretanalyser/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	secretanalyser "github.com/epifi/gamma/api/webfe/secretanalyser"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockSecretAnalyserClient is a mock of SecretAnalyserClient interface.
type MockSecretAnalyserClient struct {
	ctrl     *gomock.Controller
	recorder *MockSecretAnalyserClientMockRecorder
}

// MockSecretAnalyserClientMockRecorder is the mock recorder for MockSecretAnalyserClient.
type MockSecretAnalyserClientMockRecorder struct {
	mock *MockSecretAnalyserClient
}

// NewMockSecretAnalyserClient creates a new mock instance.
func NewMockSecretAnalyserClient(ctrl *gomock.Controller) *MockSecretAnalyserClient {
	mock := &MockSecretAnalyserClient{ctrl: ctrl}
	mock.recorder = &MockSecretAnalyserClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSecretAnalyserClient) EXPECT() *MockSecretAnalyserClientMockRecorder {
	return m.recorder
}

// GetAnalyserLandingScreen mocks base method.
func (m *MockSecretAnalyserClient) GetAnalyserLandingScreen(ctx context.Context, in *secretanalyser.GetAnalyserLandingScreenRequest, opts ...grpc.CallOption) (*secretanalyser.GetAnalyserLandingScreenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnalyserLandingScreen", varargs...)
	ret0, _ := ret[0].(*secretanalyser.GetAnalyserLandingScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnalyserLandingScreen indicates an expected call of GetAnalyserLandingScreen.
func (mr *MockSecretAnalyserClientMockRecorder) GetAnalyserLandingScreen(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnalyserLandingScreen", reflect.TypeOf((*MockSecretAnalyserClient)(nil).GetAnalyserLandingScreen), varargs...)
}

// GetSecretAnalyser mocks base method.
func (m *MockSecretAnalyserClient) GetSecretAnalyser(ctx context.Context, in *secretanalyser.GetSecretAnalyserRequest, opts ...grpc.CallOption) (*secretanalyser.GetSecretAnalyserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSecretAnalyser", varargs...)
	ret0, _ := ret[0].(*secretanalyser.GetSecretAnalyserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecretAnalyser indicates an expected call of GetSecretAnalyser.
func (mr *MockSecretAnalyserClientMockRecorder) GetSecretAnalyser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecretAnalyser", reflect.TypeOf((*MockSecretAnalyserClient)(nil).GetSecretAnalyser), varargs...)
}

// MockSecretAnalyserServer is a mock of SecretAnalyserServer interface.
type MockSecretAnalyserServer struct {
	ctrl     *gomock.Controller
	recorder *MockSecretAnalyserServerMockRecorder
}

// MockSecretAnalyserServerMockRecorder is the mock recorder for MockSecretAnalyserServer.
type MockSecretAnalyserServerMockRecorder struct {
	mock *MockSecretAnalyserServer
}

// NewMockSecretAnalyserServer creates a new mock instance.
func NewMockSecretAnalyserServer(ctrl *gomock.Controller) *MockSecretAnalyserServer {
	mock := &MockSecretAnalyserServer{ctrl: ctrl}
	mock.recorder = &MockSecretAnalyserServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSecretAnalyserServer) EXPECT() *MockSecretAnalyserServerMockRecorder {
	return m.recorder
}

// GetAnalyserLandingScreen mocks base method.
func (m *MockSecretAnalyserServer) GetAnalyserLandingScreen(arg0 context.Context, arg1 *secretanalyser.GetAnalyserLandingScreenRequest) (*secretanalyser.GetAnalyserLandingScreenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnalyserLandingScreen", arg0, arg1)
	ret0, _ := ret[0].(*secretanalyser.GetAnalyserLandingScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnalyserLandingScreen indicates an expected call of GetAnalyserLandingScreen.
func (mr *MockSecretAnalyserServerMockRecorder) GetAnalyserLandingScreen(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnalyserLandingScreen", reflect.TypeOf((*MockSecretAnalyserServer)(nil).GetAnalyserLandingScreen), arg0, arg1)
}

// GetSecretAnalyser mocks base method.
func (m *MockSecretAnalyserServer) GetSecretAnalyser(arg0 context.Context, arg1 *secretanalyser.GetSecretAnalyserRequest) (*secretanalyser.GetSecretAnalyserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSecretAnalyser", arg0, arg1)
	ret0, _ := ret[0].(*secretanalyser.GetSecretAnalyserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecretAnalyser indicates an expected call of GetSecretAnalyser.
func (mr *MockSecretAnalyserServerMockRecorder) GetSecretAnalyser(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecretAnalyser", reflect.TypeOf((*MockSecretAnalyserServer)(nil).GetSecretAnalyser), arg0, arg1)
}

// MockUnsafeSecretAnalyserServer is a mock of UnsafeSecretAnalyserServer interface.
type MockUnsafeSecretAnalyserServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeSecretAnalyserServerMockRecorder
}

// MockUnsafeSecretAnalyserServerMockRecorder is the mock recorder for MockUnsafeSecretAnalyserServer.
type MockUnsafeSecretAnalyserServerMockRecorder struct {
	mock *MockUnsafeSecretAnalyserServer
}

// NewMockUnsafeSecretAnalyserServer creates a new mock instance.
func NewMockUnsafeSecretAnalyserServer(ctrl *gomock.Controller) *MockUnsafeSecretAnalyserServer {
	mock := &MockUnsafeSecretAnalyserServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeSecretAnalyserServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeSecretAnalyserServer) EXPECT() *MockUnsafeSecretAnalyserServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedSecretAnalyserServer mocks base method.
func (m *MockUnsafeSecretAnalyserServer) mustEmbedUnimplementedSecretAnalyserServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedSecretAnalyserServer")
}

// mustEmbedUnimplementedSecretAnalyserServer indicates an expected call of mustEmbedUnimplementedSecretAnalyserServer.
func (mr *MockUnsafeSecretAnalyserServerMockRecorder) mustEmbedUnimplementedSecretAnalyserServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedSecretAnalyserServer", reflect.TypeOf((*MockUnsafeSecretAnalyserServer)(nil).mustEmbedUnimplementedSecretAnalyserServer))
}
