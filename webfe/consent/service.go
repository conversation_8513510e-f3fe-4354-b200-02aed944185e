package consent

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/async/goroutine"

	"github.com/epifi/be-common/pkg/epifigrpc"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/comms"
	upPb "github.com/epifi/gamma/api/comms/user_preference"
	beConsentPb "github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/webfe"
	consentpb "github.com/epifi/gamma/api/webfe/consent"
)

var (
	webFeConsentToBeConsent = map[webfe.ConsentType]beConsentPb.ConsentType{
		webfe.ConsentType_CONSENT_TYPE_FI_TNC:                  beConsentPb.ConsentType_FI_TNC,
		webfe.ConsentType_CONSENT_TYPE_FED_TNC:                 beConsentPb.ConsentType_FED_TNC,
		webfe.ConsentType_CONSENT_TYPE_FI_PRIVACY_POLICY:       beConsentPb.ConsentType_FI_PRIVACY_POLICY,
		webfe.ConsentType_CONSENT_TYPE_FI_WEALTH_TNC:           beConsentPb.ConsentType_FI_WEALTH_TNC,
		webfe.ConsentType_CONSENT_TYPE_CREDIT_REPORT_DATA_PULL: beConsentPb.ConsentType_CREDIT_REPORT_DATA_PULL,
	}
)

type Service struct {
	consentClient       beConsentPb.ConsentClient
	userCommsPrefClient upPb.UserPreferenceClient
}

func NewService(consentClient beConsentPb.ConsentClient,
	userCommsPrefClient upPb.UserPreferenceClient) *Service {
	return &Service{
		consentClient:       consentClient,
		userCommsPrefClient: userCommsPrefClient,
	}
}

var _ consentpb.ConsentServer = &Service{}

func (s *Service) RecordConsents(ctx context.Context, req *consentpb.RecordConsentsRequest) (*consentpb.RecordConsentsResponse, error) {
	var errRes = func(status *rpcPb.Status) (*consentpb.RecordConsentsResponse, error) {
		return &consentpb.RecordConsentsResponse{
			Status: status,
			RespHeader: &header.ResponseHeader{
				Status: status,
			},
		}, nil
	}

	actorId := req.GetReq().GetAuth().GetActorId()
	clientReqId := req.GetClientReqId()

	// record whatsapp consent
	goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
		_ = s.recordWAConsent(ctx, actorId, req.GetRecordWhatsappConsent())
	})

	if len(req.GetConsentList()) == 0 {
		logger.Error(ctx, "consent list is empty", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return &consentpb.RecordConsentsResponse{
			Status: rpcPb.StatusOk(),
		}, nil
	}

	owner, present := commontypes.Owner_value[req.GetOwner()]
	if !present || owner == 0 {
		logger.Error(ctx, "invalid owner", zap.String(logger.OWNERSHIP, req.GetOwner()))
		return errRes(rpcPb.StatusInvalidArgumentWithDebugMsg("invalid owner"))
	}

	var consentList []beConsentPb.ConsentType
	for _, consentStr := range req.GetConsentList() {
		consentVal, ok := webfe.ConsentType_value[consentStr]
		if !ok {
			logger.Error(ctx, "invalid consent type", zap.String(logger.CONSENT_TYPE, consentStr))
			return errRes(rpcPb.StatusInvalidArgumentWithDebugMsg("invalid consent type"))
		}

		webFeConsent := webfe.ConsentType(consentVal)
		beConsent, ok := webFeConsentToBeConsent[webFeConsent]
		if !ok {
			logger.Error(ctx, "invalid consent type", zap.String(logger.CONSENT_TYPE, consentStr))
			return errRes(rpcPb.StatusInvalidArgumentWithDebugMsg("invalid consent type"))
		}
		consentList = append(consentList, beConsent)
	}

	// check consent requirement
	checkReqResp, checkReqErr := s.consentClient.CheckConsentRequirement(ctx, &beConsentPb.CheckConsentRequirementRequest{
		ActorId:      actorId,
		Owner:        commontypes.Owner(owner),
		ConsentTypes: consentList,
	})

	if checkReqErr != nil {
		logger.Error(ctx, "error faced while checking consent requirement", zap.Error(checkReqErr))
		return errRes(rpcPb.StatusInternalWithDebugMsg(checkReqErr.Error()))
	}

	if !checkReqResp.GetIsConsentRequired() {
		logger.Info(ctx, "consent not required for the given request", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return errRes(rpcPb.StatusAlreadyExists())
	}

	var consentRequestInfo []*beConsentPb.ConsentRequestInfo
	for _, consentType := range consentList {
		consentRequestInfo = append(consentRequestInfo, &beConsentPb.ConsentRequestInfo{
			ConsentType: consentType,
			ClientReqId: clientReqId,
		})
	}

	recordResp, err := s.consentClient.RecordConsents(ctx, &beConsentPb.RecordConsentsRequest{
		ActorId:  actorId,
		Device:   req.GetReq().GetAuth().GetDevice(),
		Owner:    commontypes.Owner(owner),
		Consents: consentRequestInfo,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error faced while recording consent", zap.Error(err))
		return errRes(rpcPb.StatusInternalWithDebugMsg(err.Error()))
	case recordResp.GetStatus().IsAlreadyExists():
		logger.Info(ctx, "consent already exists for the given request",
			zap.String(logger.CLIENT_REQUEST_ID, clientReqId))
		return errRes(rpcPb.StatusAlreadyExists())
	}

	if rpcErr := epifigrpc.RPCError(recordResp, err); rpcErr != nil {
		logger.Error(ctx, "error faced while recording consent", zap.Error(rpcErr))
		return errRes(rpcPb.StatusInternalWithDebugMsg(rpcErr.Error()))
	}

	return &consentpb.RecordConsentsResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

// Record Whatsapp consent
func (s *Service) recordWAConsent(ctx context.Context, actorId string, recordWaConsent commontypes.BooleanEnum) error {

	if recordWaConsent == commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		logger.Info(ctx, "no WA consent decision found in request")
		return fmt.Errorf("no WA consent decision found in request")
	}

	var pref upPb.Preference
	if recordWaConsent == commontypes.BooleanEnum_TRUE {
		pref = upPb.Preference_ON
	} else {
		pref = upPb.Preference_OFF
	}

	res, err := s.userCommsPrefClient.CreatePreference(ctx, &upPb.CreatePreferenceRequest{
		ActorId:    actorId,
		Medium:     comms.Medium_WHATSAPP,
		Category:   comms.Category_CATEGORY_PROMOTIONAL,
		Preference: pref,
	})

	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "failed to create preference for category promotion", zap.Error(rpcErr))
		return rpcErr
	}

	res, err = s.userCommsPrefClient.CreatePreference(ctx, &upPb.CreatePreferenceRequest{
		ActorId:    actorId,
		Medium:     comms.Medium_WHATSAPP,
		Category:   comms.Category_CATEGORY_TRANSACTIONAL,
		Preference: pref,
	})

	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "failed to create preference for category transactional", zap.Error(rpcErr))
		return rpcErr
	}

	return nil
}

func (s *Service) AuthFuncOverride(ctx context.Context, authClient auth.AuthClient, authH *header.AuthHeader) (*auth.ValidateTokenResponse, error) {
	return authClient.ValidateToken(ctx, &auth.ValidateTokenRequest{
		Token:     authH.GetAccessToken(),
		TokenType: auth.TokenType_WEB_ACCESS_TOKEN,
		Device:    authH.GetDevice(),
	})
}
