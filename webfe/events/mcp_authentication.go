package events

import (
	"time"

	"github.com/epifi/be-common/pkg/events"
	"github.com/fatih/structs"
	"github.com/google/uuid"
)

const EventMCPAuthentication = "MCPAuthentication"

type MCPAuthentication struct {
	ActorId   string
	SessionId string
	Status    bool
	EventId   string
	Timestamp time.Time
	EventType string
}

func NewMCPAuthentication(actorId, sessionId string, status bool) *MCPAuthentication {
	return &MCPAuthentication{
		ActorId:   actorId,
		SessionId: sessionId,
		Status:    status,
		EventType: events.EventTrack,
		EventId:   uuid.New().String(),
		Timestamp: time.Now(),
	}
}

func (e *MCPAuthentication) GetEventType() string {
	return e.EventType
}

func (e *MCPAuthentication) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *MCPAuthentication) GetEventTraits() map[string]interface{} {
	return nil
}

func (e *MCPAuthentication) GetEventId() string {
	return e.EventId
}

func (e *MCPAuthentication) GetUserId() string {
	return e.ActorId
}

func (e *MCPAuthentication) GetEventName() string {
	return EventMCPAuthentication
}

func (e *MCPAuthentication) GetProspectId() string {
	return ""
}
