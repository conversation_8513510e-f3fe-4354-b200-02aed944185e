// Code generated by MockGen. DO NOT EDIT.
// Source: logger.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	extacct "github.com/epifi/gamma/api/savings/extacct"
	user "github.com/epifi/gamma/api/user"
	gomock "github.com/golang/mock/gomock"
)

// MockEventLogger is a mock of EventLogger interface.
type MockEventLogger struct {
	ctrl     *gomock.Controller
	recorder *MockEventLoggerMockRecorder
}

// MockEventLoggerMockRecorder is the mock recorder for MockEventLogger.
type MockEventLoggerMockRecorder struct {
	mock *MockEventLogger
}

// NewMockEventLogger creates a new mock instance.
func NewMockEventLogger(ctrl *gomock.Controller) *MockEventLogger {
	mock := &MockEventLogger{ctrl: ctrl}
	mock.recorder = &MockEventLoggerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEventLogger) EXPECT() *MockEventLoggerMockRecorder {
	return m.recorder
}

// LogMCPAuthentication mocks base method.
func (m *MockEventLogger) LogMCPAuthentication(ctx context.Context, actorId, sessionId string, status bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "LogMCPAuthentication", ctx, actorId, sessionId, status)
}

// LogMCPAuthentication indicates an expected call of LogMCPAuthentication.
func (mr *MockEventLoggerMockRecorder) LogMCPAuthentication(ctx, actorId, sessionId, status interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LogMCPAuthentication", reflect.TypeOf((*MockEventLogger)(nil).LogMCPAuthentication), ctx, actorId, sessionId, status)
}

// LogUserAlternateAccountDetailsVerificationServer mocks base method.
func (m *MockEventLogger) LogUserAlternateAccountDetailsVerificationServer(ctx context.Context, actorId string, verificationStatus extacct.OverallStatus, failureReason extacct.FailureReason) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "LogUserAlternateAccountDetailsVerificationServer", ctx, actorId, verificationStatus, failureReason)
}

// LogUserAlternateAccountDetailsVerificationServer indicates an expected call of LogUserAlternateAccountDetailsVerificationServer.
func (mr *MockEventLoggerMockRecorder) LogUserAlternateAccountDetailsVerificationServer(ctx, actorId, verificationStatus, failureReason interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LogUserAlternateAccountDetailsVerificationServer", reflect.TypeOf((*MockEventLogger)(nil).LogUserAlternateAccountDetailsVerificationServer), ctx, actorId, verificationStatus, failureReason)
}

// LogUserIdentityVerificationServer mocks base method.
func (m *MockEventLogger) LogUserIdentityVerificationServer(ctx context.Context, actorId string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "LogUserIdentityVerificationServer", ctx, actorId)
}

// LogUserIdentityVerificationServer indicates an expected call of LogUserIdentityVerificationServer.
func (mr *MockEventLoggerMockRecorder) LogUserIdentityVerificationServer(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LogUserIdentityVerificationServer", reflect.TypeOf((*MockEventLogger)(nil).LogUserIdentityVerificationServer), ctx, actorId)
}

// LogUserPANVerificationServer mocks base method.
func (m *MockEventLogger) LogUserPANVerificationServer(ctx context.Context, actorId string, accessRevokeDetails *user.AccessRevokeDetails, nextScreen string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "LogUserPANVerificationServer", ctx, actorId, accessRevokeDetails, nextScreen)
}

// LogUserPANVerificationServer indicates an expected call of LogUserPANVerificationServer.
func (mr *MockEventLoggerMockRecorder) LogUserPANVerificationServer(ctx, actorId, accessRevokeDetails, nextScreen interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LogUserPANVerificationServer", reflect.TypeOf((*MockEventLogger)(nil).LogUserPANVerificationServer), ctx, actorId, accessRevokeDetails, nextScreen)
}
