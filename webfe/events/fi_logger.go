package events

import (
	"context"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/api/user"
)

type FiEventLogger struct {
	eventBroker events.Broker
}

func NewFiEventLogger(eventBroker events.Broker) EventLogger {
	return &FiEventLogger{
		eventBroker: eventBroker,
	}
}

var _ EventLogger = (*FiEventLogger)(nil)

func (f FiEventLogger) LogUserIdentityVerificationServer(ctx context.Context, actorId string) {
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		f.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), NewUserIdentityVerificationServer(actorId))
	})
}

func (f FiEventLogger) LogUserPANVerificationServer(ctx context.Context, actorId string, accessRevokeDetails *user.AccessRevokeDetails, nextScreen string) {
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		accountStatus := "ACTIVE"
		if accessRevokeDetails.GetAccessRevokeStatus() == user.AccessRevokeStatus_ACCESS_REVOKE_STATUS_BLOCKED &&
			(accessRevokeDetails.GetReason() == user.AccessRevokeReason_ACCESS_REVOKE_REASON_MIN_KYC_ACCOUNT_EXPIRY ||
				accessRevokeDetails.GetReason() == user.AccessRevokeReason_ACCESS_REVOKE_REASON_LSO_USER_VKYC_PENDING) {
			accountStatus = "CLOSED"
		}
		f.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), NewUserPANVerificationServerEvent(actorId, accountStatus, nextScreen, accessRevokeDetails.GetReason().String()))
	})
}

func (f FiEventLogger) LogUserAlternateAccountDetailsVerificationServer(ctx context.Context, actorId string, verificationStatus extacct.OverallStatus, failureReason extacct.FailureReason) {
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		f.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), NewUserAlternateAccountDetailsVerificationServerEvent(actorId, verificationStatus.String(), failureReason.String()))
	})
}

func (f FiEventLogger) LogMCPAuthentication(ctx context.Context, actorId string, sessionId string, status bool) {
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		f.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), NewMCPAuthentication(actorId, sessionId, status))
	})
}
