package events

import (
	"context"

	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/api/user"
)

//go:generate mockgen -source=logger.go -destination=./mocks/event_logger.go -package=mocks
type EventLogger interface {
	// web account closure events

	// LogUserIdentityVerificationServer is triggered after successful phone/email otp verification
	LogUserIdentityVerificationServer(ctx context.Context, actorId string)
	// LogUserPANVerificationServer is triggered after successful pan number verification
	LogUserPANVerificationServer(ctx context.Context, actorId string, accessRevokeDetails *user.AccessRevokeDetails, nextScreen string)
	// LogUserAlternateAccountDetailsVerificationServer is triggered after ShareAlternateAccountForBalanceTransfer api is triggered
	LogUserAlternateAccountDetailsVerificationServer(ctx context.Context, actorId string, verificationStatus extacct.OverallStatus, failureReason extacct.FailureReason)
	// LogMCPAuthentication is triggered after OTP verification (true for success, false for failure)
	LogMCPAuthentication(ctx context.Context, actorId string, sessionId string, status bool)
}
