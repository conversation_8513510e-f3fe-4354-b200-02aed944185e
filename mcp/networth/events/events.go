package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

const MCPChatEvent = "MCPChat"

// MCPChat tracks session-level chat events with tool usage and authentication status
type MC<PERSON>hat struct {
	EventType            string
	EventId              string
	ActorId              string
	SessionId            string
	Timestamp            time.Time
	CreditReportToolUsed bool
	NetWorthToolUsed     bool
	TransactionsToolUsed bool
}

func NewMCPChat(actorId, sessionId string, creditReportUsed, netWorthUsed, transactionsUsed bool) *MCPChat {
	return &MCPChat{
		ActorId:              actorId,
		SessionId:            sessionId,
		EventType:            events.EventTrack,
		EventId:              uuid.New().String(),
		Timestamp:            time.Now(),
		CreditReportToolUsed: creditReportUsed,
		NetWorthToolUsed:     netWorthUsed,
		TransactionsToolUsed: transactionsUsed,
	}
}

func (e *MCPChat) GetEventType() string {
	return e.EventType
}

func (e *MCPChat) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *MCPChat) GetEventTraits() map[string]interface{} {
	return nil
}

func (e *MCPChat) GetEventId() string {
	return e.EventId
}

func (e *MCPChat) GetUserId() string {
	return e.ActorId
}

func (e *MCPChat) GetEventName() string {
	return MCPChatEvent
}

func (e *MCPChat) GetProspectId() string {
	return ""
}
